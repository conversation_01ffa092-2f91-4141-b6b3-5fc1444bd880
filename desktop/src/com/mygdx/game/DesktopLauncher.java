package com.mygdx.game;

import com.badlogic.gdx.Files;
import com.badlogic.gdx.backends.lwjgl.LwjglApplication;
import com.badlogic.gdx.backends.lwjgl.LwjglApplicationConfiguration;
import com.teamobi.mobiarmy2.MainGame;

public class DesktopLauncher {
    public static void main(String[] arg) {
        LwjglApplicationConfiguration config = new LwjglApplicationConfiguration();
        config.foregroundFPS =40;
        config.addIcon("res/icon.png", Files.FileType.Internal);
        config.title ="Army2";

        config.width = 1280;
        config.height = 720;
        config.resizable =false;

        MainGame game = new MainGame();
        new LwjglApplication(game, config);
    }
}
