sourceCompatibility = 1.8
sourceSets.main.java.srcDirs = ["src/"]
sourceSets.main.resources.srcDirs = ["../assets"]

project.ext.mainClassName = "com.mygdx.game.DesktopLauncher"
project.ext.assetsDir = file("assets")

import org.gradle.internal.os.OperatingSystem

tasks.register('run', JavaExec) {
    dependsOn classes
    mainClass = project.mainClassName
    classpath = sourceSets.main.runtimeClasspath
    standardInput = System.in
    workingDir = project.assetsDir
    ignoreExitValue = true

    if (OperatingSystem.current() == OperatingSystem.MAC_OS) {
        // Required to run on macOS
        jvmArgs += "-XstartOnFirstThread"
    }
}

tasks.register('debug', JavaExec) {
    dependsOn classes
    mainClass = project.mainClassName
    classpath = sourceSets.main.runtimeClasspath
    standardInput = System.in
    workingDir = project.assetsDir
    ignoreExitValue = true
    debug = true
}

tasks.register('dist', Jar) {
    duplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    manifest {
        attributes 'Main-Class': project.mainClassName
    }
    dependsOn configurations.runtimeClasspath
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    with jar
}


dist.dependsOn classes

eclipse.project.name = appName + "-desktop"
