<config>
  <executableName>${app.executable}</executableName>
  <mainClass>${app.mainclass}</mainClass>
  <os>ios</os>
  <target>ios</target>
  <iosInfoPList>Info.plist.xml</iosInfoPList>
  <treeShaker>conservative</treeShaker>
  <resources>
    <resource>
      <directory>assets</directory>
      <includes>
        <include>**</include>
      </includes>
      <skipPngCrush>true</skipPngCrush>
    </resource>
    <resource>
      <directory>data</directory>
    </resource>
  </resources>
  <forceLinkClasses>
    <pattern>com.badlogic.gdx.scenes.scene2d.ui.*</pattern>
    <pattern>com.badlogic.gdx.graphics.g3d.particles.**</pattern>
    <pattern>com.android.okhttp.HttpHandler</pattern>
    <pattern>com.android.okhttp.HttpsHandler</pattern>
    <pattern>com.android.org.conscrypt.**</pattern>
    <pattern>com.android.org.bouncycastle.jce.provider.BouncyCastleProvider</pattern>
    <pattern>com.android.org.bouncycastle.jcajce.provider.keystore.BC$Mappings</pattern>
    <pattern>com.android.org.bouncycastle.jcajce.provider.keystore.bc.BcKeyStoreSpi</pattern>
    <pattern>com.android.org.bouncycastle.jcajce.provider.keystore.bc.BcKeyStoreSpi$Std</pattern>
    <pattern>com.android.org.bouncycastle.jce.provider.PKIXCertPathValidatorSpi</pattern>
    <pattern>com.android.org.bouncycastle.crypto.digests.AndroidDigestFactoryOpenSSL</pattern>
    <pattern>org.apache.harmony.security.provider.cert.DRLCertFactory</pattern>
    <pattern>org.apache.harmony.security.provider.crypto.CryptoProvider</pattern>
  </forceLinkClasses>
  <libs>
      <lib>z</lib>   
  </libs>
  <frameworks>
    <framework>UIKit</framework>
    <framework>QuartzCore</framework>
    <framework>CoreGraphics</framework>
    <framework>OpenAL</framework>
    <framework>AudioToolbox</framework>
    <framework>AVFoundation</framework>
    <framework>GameController</framework>
  </frameworks>
</config>
