# This file is used by the Eclipse ADT plugin. It is unnecessary for IDEA and Android Studio projects, which
# configure Proguard and the Android target via the build.gradle file.

# To enable ProGuard to work with Eclipse ADT, uncomment this (available properties: sdk.dir, user.home)
# and ensure proguard.jar in the Android SDK is up to date (or alternately reduce the android target to 23 or lower):
# proguard.config=${sdk.dir}/tools/proguard/proguard-android.txt:proguard-rules.pro

# Project target.
target=android-19
