## Java

*.class
*.war
*.ear
hs_err_pid*

## Robovm
/ios/robovm-build/

## GWT
/html/war/
/html/gwt-unitCache/
.apt_generated/
.gwt/
gwt-unitCache/
www-test/
.gwt-tmp/

## Android Studio and Intellij and Android in general
/android/libs/armeabi-v7a/
/android/libs/arm64-v8a/
/android/libs/x86/
/android/libs/x86_64/
/android/gen/
.idea/
*.ipr
*.iws
*.iml
/android/out/
com_crashlytics_export_strings.xml

## Eclipse

.classpath
.project
.metadata/
/android/bin/
/core/bin/
/desktop/bin/
/html/bin/
/ios/bin/
*.tmp
*.bak
*.swp
*~.nib
.settings/
.loadpath
.externalToolBuilders/
*.launch

## NetBeans

/nbproject/private/
/android/nbproject/private/
/core/nbproject/private/
/desktop/nbproject/private/
/html/nbproject/private/
/ios/nbproject/private/

/build/
/android/build/
/core/build/
/desktop/build/
/html/build/
/ios/build/

/nbbuild/
/android/nbbuild/
/core/nbbuild/
/desktop/nbbuild/
/html/nbbuild/
/ios/nbbuild/

/dist/
/android/dist/
/core/dist/
/desktop/dist/
/html/dist/
/ios/dist/

/nbdist/
/android/nbdist/
/core/nbdist/
/desktop/nbdist/
/html/nbdist/
/ios/nbdist/

nbactions.xml
nb-configuration.xml

## Gradle

/local.properties
.gradle/
gradle-app.setting
/build/
/android/build/
/core/build/
/desktop/build/
/html/build/
/ios/build/

## OS Specific
.DS_Store
Thumbs.db

## iOS
/ios/xcode/*.xcodeproj/*
!/ios/xcode/*.xcodeproj/xcshareddata
!/ios/xcode/*.xcodeproj/project.pbxproj
/ios/xcode/native/
/ios/IOSLauncher.app
/ios/IOSLauncher.app.dSYM
/jad/nbproject/private/
/jad/build/
/jad/dist/
/jar/nbproject/private/
/Army2-Midlet/nbproject/private/
/Army2/nbproject/private/
