package network;

public class Cmd_Client2Server {
    public static final short LOGIN = 1;
    public static final byte LOGOUT = 2;
    public static final byte CHAT_TO = 5;
    public static final byte REQUEST_ROOMLIST = 6;
    public static final byte REQUEST_BOARDLIST = 7;
    public static final byte JOIN_BOARD = 8;
    public static final byte LEAVE_BOARD = 15;
    public static final byte READY = 16;
    public static final byte SET_MONEY = 19;
    public static final byte SET_PASS = 18;
    public static final byte CHAT_TO_BOARD = 9;
    public static final byte KICK = 11;
    public static final byte JOIN_ANY_BOARD = 28;
    public static final byte REQUEST_STRONGEST = 30;
    public static final byte REQUEST_RICHEST = 31;
    public static final byte REQUEST_REGISTER = 35;
    public static final byte REQUEST_FRIENDLIST = 29;
    public static final byte SEARCH = 36;
    public static final byte ADD_FRIEND = 32;
    public static final byte DELETE_FRIEND = 33;
    public static final byte REQUEST_DETAIL = 34;
    public static final byte USER_DATA = 40;
    public static final byte REQUEST_AVATAR = 38;
    public static final byte REQUEST_AVATARLIST = 39;
    public static final byte PING = 42;
    public static final byte UPDATE_USERDATA = 41;
    public static final byte BUY_AVATAR = 43;
    public static final byte ADMIN_COMMAND = 47;
    public static final byte VERSION = 48;
    public static final byte START_ARMY = 20;
    public static final byte MOVE_ARMY = 21;
    public static final byte FIRE_ARMY = 22;
    public static final byte SHOOT_RESULT = 23;
    public static final byte UPDATE_XY = 53;
    public static final byte SET_BOARD_NAME = 54;
    public static final byte SET_PROVIDER = 58;
    public static final byte NEXT_TURN = 63;
    public static final byte SKIP = 49;
    public static final byte WIND = 25;
    public static final byte USE_ITEM = 26;
    public static final byte SET_MAX_PLAYER = 56;
    public static final byte DIE = 60;
    public static final byte CHOOSE_ITEM = 68;
    public static final byte CHOOSE_GUN = 69;
    public static final byte CHOOSE_MAP = 70;
    public static final byte CHANGE_TEAM = 71;
    public static final byte BUY_ITEM = 72;
    public static final byte CHANGE_MODE = 73;
    public static final byte BUY_GUN = 74;
    public static final byte MAP_SELECT = 75;
    public static final byte LOAD_CARD = 77;
    public static final byte FIND_PLAYER = 78;
    public static final byte CHECK_CROSS = 79;
    public static final byte CHECK_FALL = 80;
    public static final byte CHANGE_PASS = 81;
    public static final byte TRAINING = 83;
    public static final byte TRAININGSHOOT = 84;
    public static final byte REQUEST_SERVICE = 85;
    public static final byte ZING_CONNECT = 87;
    public static final byte GETSTRING = 127;
    public static final byte GET_ITEM_SLOT = 94;
    public static final byte ADD_POINT = 98;
    public static final byte RULET = 110;
    public static final byte VIP_EQUIP = -2;
    public static final byte DISCONECT = -4;
    public static final byte EMPTY_ROOM = -28;
    public static final byte CHECK_MAKE_HOLE = -92;
}
