package CLib;

import model.CRes;

public class mSound {
   public static float volumeSound = 0.0F;
   public static float volumeMusic = 0.0F;
   private static final int MAX_VOLUME = 10;
   public static int[] soundID;
   public static int CurMusic = -1;
   public static boolean isMusic = true;
   public static boolean isSound = true;
   public static SoundSystem[] music;
   public static SoundSystem[] sound;
   public static boolean isEnableSound = true;
   public static int sizeMusic = 1;
   public static int sizeSound = 3;
   public static SoundSystem[]taThan;
   public static SoundSystem[]quocVuongGa;
   public static SoundSystem[]bom;
   public static SoundSystem[]nvkien;
   public static SoundSystem[]map;
   public static SoundSystem mob_attack;


   public static void init() {
      music = new SoundSystem[sizeMusic];

      int i;
      for(i = 0; i < music.length; ++i) {
         music[i] = new SoundSystem(String.valueOf(i), true);
      }

      sound = new SoundSystem[sizeSound];

      for(i = 0; i < sound.length; ++i) {
         boolean isLOOP = false;
         sound[i] = new SoundSystem(String.valueOf(i), isLOOP);
      }
      // Initialize specific grouped sounds from assets/res/sounds
      taThan = new SoundSystem[3];
      taThan[0] = new SoundSystem("tathan_at1", false);
      taThan[1] = new SoundSystem("tathan_at2", false);
      taThan[2] = new SoundSystem("tathan_at3", false);

      quocVuongGa = new SoundSystem[2];
      quocVuongGa[0] = new SoundSystem("quocvuongga_at1", false);
      quocVuongGa[1] = new SoundSystem("quocvuongga_at2", false);

      bom = new SoundSystem[2];
      bom[0] = new SoundSystem("bom_at1", false);
      bom[1] = new SoundSystem("bom_at2", false);

      nvkien = new SoundSystem[3];
      nvkien[0] = new SoundSystem("nvkien_at1", false);
      nvkien[1] = new SoundSystem("nvkien_at2", false);
      nvkien[2] = new SoundSystem("nvkien_at3", false);

      map = new SoundSystem[4];
      map[0] = new SoundSystem("tathan_ai2", false);
      map[1] = new SoundSystem("kien_ai2", false);
      map[2] = new SoundSystem("quocvuongga_ai2", false);
      map[3] = new SoundSystem("bom", false);
      mob_attack = new SoundSystem("mob_attack", false);

      System.gc();
   }

   public static int getSoundPoolSource(int index, String fileName) {
      return index;
   }

   public static void playSound(int id, float volume, int index) {
      if (isSound) {
         if (sound != null && sound[id] != null) {
            try {
               sound[id].play(volume);
            } catch (IllegalStateException var4) {
               var4.printStackTrace();
            }
         }

      }
   }
   public static void playSoundTaThan(int id, float volume) {
      if (isSound) {
         if (taThan != null && taThan[id] != null) {
            try {
               taThan[id].play(volume);
            } catch (IllegalStateException var4) {
               var4.printStackTrace();
            }
         }

      }
   }
   public static void playSoundMobAttack(float volume) {
      if (isSound) {
         if (mob_attack != null) {
            mob_attack.play(volume);
         }
      }
   }

   public static void playSoundQuocVuongGa(int id, float volume) {
      if (isSound) {
         if (quocVuongGa != null && quocVuongGa[id] != null) {
            try {
               quocVuongGa[id].play(volume);
            } catch (IllegalStateException var4) {
               var4.printStackTrace();
            }
         }
      }
   }
   public static void playSoundBom(int id, float volume) {
      if (isSound) {
         if (bom != null && bom[id] != null) {
            try {
               bom[id].play(volume);
            } catch (IllegalStateException var4) {
               var4.printStackTrace();
            }
         }
      }
   }
   public static void playSoundNvKien(int id, float volume) {
      if (isSound) {
         if (nvkien != null && nvkien[id] != null) {
            try {
               nvkien[id].play(volume);
            } catch (IllegalStateException var4) {
               var4.printStackTrace();
            }
         }
      }
   }

   public static void SetLoopSound(int id, float volume, int loop) {
   }

   public static void UnSetLoopAll() {
   }

   public static void playMus(int id, float volume, boolean isLoop) {
      if (isMusic) {
         if (music != null) {
            for(int i = 0; i < music.length; ++i) {
               if (music[i] != null && music[i].isPlaying() && i != id) {
                  music[i].pause();
               }
            }
         }

         if (id >= 0 && id <= music.length - 1) {
            try {
               music[id].setVolume(volume, volume);
               music[id].setLooping(isLoop);
               music[id].play(volume);
            } catch (IllegalStateException var4) {
               var4.printStackTrace();
            }

         }
      }
   }

   public static void pauseMusic(int id) {
   }

   public static void pauseCurMusic() {
      for(int i = 0; i < music.length; ++i) {
         if (music[i].isPlaying()) {
            music[i].pause();
            CurMusic = i;
         }
      }

   }

   public static void resumeMusic(int id) {
   }

   public static void resumeAll() {
   }

   public static void releaseAll() {
   }

   public static void stopAll() {
   }

   public static void stopSoundAll() {
      if (sound != null) {
         for(int i = 0; i < sound.length; ++i) {
            if (sound[i] != null) {
               sound[i].stop();
            }
         }
      }

   }

   public static void setVolume(int id, int index, int soundVolume) {
   }

   public static void setVolume(int volumeSound) {
      CRes.saveRMSInt("sound", volumeSound);
      mSound.volumeSound = (float)volumeSound / 100.0F;
   }

   // Method to play background music based on map ID
   public static void playMapMusic(int mapID) {
      if (!isMusic) {
         return;
      }
      if (map != null) {
         for(int i = 0; i < map.length; ++i) {
            if (map[i] != null) {
               map[i].stop();
            }
         }
      }
      
      // Play music based on map ID
      switch (mapID) {
         case 40: // Tà thần
            if (map != null && map[0] != null) {
               try {
                  map[0].play(volumeSound);
               } catch (Exception e) {
                  e.printStackTrace();
               }
            }
            break;
         case 41: // Kiến chúa
            if (map != null && map[1] != null) {
               try {
                  map[1].play(volumeSound);
               } catch (Exception e) {
                  e.printStackTrace();
               }
            }
            break;
         case 44: // Quốc vương gà
            if (map != null && map[2] != null) {
               try {
                  map[2].play(volumeSound);
               } catch (Exception e) {
                  e.printStackTrace();
               }
            }
            break;
         case 45: // Bom
            if (map != null && map[3] != null) {
               try {
                  map[3].play(volumeSound);
               } catch (Exception e) {
                  e.printStackTrace();
               }
            }
            break;
         default:
            // For other maps, play default music or no music
            break;
      }
   }

   // Method to stop all map music when leaving map
   public static void stopMapMusic() {
      
      // Stop current map music
      if (map != null) {
         for(int i = 0; i < map.length; ++i) {
            if (map[i] != null) {
               map[i].stop();
            }
         }
      }
      
   }
}
