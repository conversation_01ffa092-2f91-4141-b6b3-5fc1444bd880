package CLib;

class EasingEnum {
    public static byte EaseInQuad = 0;
    public static byte EaseOutQuad = 1;
    public static byte EaseInOutQuad = 2;
    public static byte EaseInCubic = 3;
    public static byte EaseOutCubic = 4;
    public static byte EaseInOutCubic = 5;
    public static byte EaseInQuart = 6;
    public static byte EaseOutQuart = 7;
    public static byte EaseInOutQuart = 8;
    public static byte EaseInQuint = 9;
    public static byte EaseOutQuint = 10;
    public static byte EaseInOutQuint = 11;
    public static byte EaseInSine = 12;
    public static byte EaseOutSine = 13;
    public static byte EaseInOutSine = 14;
    public static byte EaseInExpo = 15;
    public static byte EaseOutExpo = 16;
    public static byte EaseInOutExpo = 17;
    public static byte EaseInCirc = 18;
    public static byte EaseOutCirc = 19;
    public static byte EaseInOutCirc = 20;
    public static byte Linear = 21;
    public static byte Spring = 22;
    public static byte EaseInBounce = 23;
    public static byte EaseOutBounce = 24;
    public static byte EaseInOutBounce = 25;
    public static byte EaseInBack = 26;
    public static byte EaseOutBack = 27;
    public static byte EaseInOutBack = 28;
    public static byte EaseInElastic = 29;
    public static byte EaseOutElastic = 30;
    public static byte EaseInOutElastic = 31;
}
