package model;

public class Language {
    public static int language = 0;
    public static final String[] sound = new String[]{"Bật âm thanh?"};
    public static final String[] soundOn = new String[]{"Bật"};
    public static final String[] soundOff = new String[]{"Im lặng"};
    public static final String[] forward = new String[]{"Qua nhanh"};
    public static final String[] reg = new String[]{"Đăng ký"};
    public static final String[] signIn = new String[]{"Đăng nhập"};
    public static final String[] delete = new String[]{"Xoá"};
    public static final String[] id = new String[]{"Tên TK"};
    public static final String[] pass = new String[]{"Mật khẩu"};
    public static final String[] email_phone = new String[]{"Email"};
    public static final String[] getNewID1 = new String[]{"Nếu chưa có tài khoản,"};
    public static final String[] getNewID2 = new String[]{"xin bấm đăng ký"};
    public static final String[] connecting = new String[]{"Đang kết nối"};
    public static final String[] pleaseWait = new String[]{"Xin chờ..."};
    public static final String[] yes = new String[]{"Có"};
    public static final String[] no = new String[]{"Không"};
    public static final String[] invite = new String[]{"mời bạn chơi"};
    public static final String[] startGame = new String[]{"BẮT ĐẦU"};
    public static final String[] shop = new String[]{"CỬA HÀNG ITEM"};
    public static final String[] news = new String[]{"TIN TỨC MỚI"};
    public static final String[] charge = new String[]{"NẠP TIỀN"};
    public static final String[] charge2 = new String[]{"Nạp tiền"};
    public static final String[] information = new String[]{"XEM THÔNG TIN"};
    public static final String[] option = new String[]{"CẤU HÌNH"};
    public static final String[] playNow = new String[]{"CHƠI GAME"};
    public static final String[] toarea = new String[]{"CHƠI GAME"};
    public static final String[] playerSelect = new String[]{"CHỌN NHÂN VẬT"};
    public static final String[] training = new String[]{"LUYỆN TẬP"};
    public static final String[] event = new String[]{"SỰ KIỆN"};
    public static final String[] topScore = new String[]{"BẢNG XẾP HẠNG"};
    public static final String[] FRIEND = new String[]{"BẠN BÈ"};
    public static final String[] achievement = new String[]{"NHIỆM VỤ"};
    public static final String[] otherGame = new String[]{"GAME KHÁC"};
    public static final String[] name = new String[]{"Tên"};
    public static final String[] money = new String[]{"Tiền"};
    public static final String[] select = new String[]{"Chọn"};
    public static final String[] exit = new String[]{"Thoát"};
    public static final String[] back = new String[]{"Trở ra"};
    public static final String[] close = new String[]{"Đóng"};
    public static final String[] update = new String[]{"Cập nhật"};
    public static final String[] enter = new String[]{"Vào"};
    public static final String[] room = new String[]{"Phòng"};
    public static final String[] ROOM = new String[]{"PHÒNG"};
    public static final String[] change_pass = new String[]{"ĐỔI PASSWORD"};
    public static final String[] area = new String[]{"Khu vực"};
    public static final String[] battleArea = new String[]{"Khu vực chơi"};
    public static final String[] selectMap = new String[]{"Chọn bản đồ"};
    public static final String[] findFriend = new String[]{"Tìm bạn chơi"};
    public static final String[] changeTeam = new String[]{"Đổi phe"};
    public static final String[] friend = new String[]{"Bạn bè"};
    public static final String[] mess = new String[]{"Tin nhắn"};
    public static final String[] MESS = new String[]{"TIN NHẮN"};
    public static final String[] leaveBattle = new String[]{"Rời trận chiến"};
    public static final String[] setMoney = new String[]{"Đặt tiền cược"};
    public static final String[] setPass = new String[]{"Đặt mật khẩu"};
    public static final String[] setPerson = new String[]{"Đặt số người"};
    public static final String[] setBoardName = new String[]{"Đặt tên trận chiến"};
    public static final String[] bulletNumber = new String[]{"Số đạn"};
    public static final String[] damage = new String[]{"Sát thương"};
    public static final String[] windEffect = new String[]{"Ma sát gió"};
    public static final String[][] tenQuanHam = new String[][]{{"Sơ cấp 1", "Sơ cấp 2", "Sơ cấp 3", "Sơ cấp 4", "Trung cấp 1", "Trung cấp 2", "Trung cấp 3", "Trung cấp 4", "Cao cấp 1", "Cao cấp 2", "Cao cấp 3", "Cao cấp 4", "Siêu cấp 1", "Siêu cấp 2", "Siêu cấp 3", "Siêu cấp 4"}};
    public static final String[] training1 = new String[]{"Xin chào các bạn đến với phần luyện tập của MOBIARMY."};
    public static final String[] training2 = new String[]{"Dùng các phím mũi tên qua lại để điều khiển nhân vật. Hãy thử xem."};
    public static final String[] training3 = new String[]{"Thanh lực màu xanh bên dưới cho biết quãng đường bạn có thể đi."};
    public static final String[] training4 = new String[]{"Dùng các phím mũi tên lên xuống để nhắm. Nhấn và giữ phím giữa để canh lực và bắn."};
    public static final String[] training5 = new String[]{"Hãy cố gắng bắn rơi tên lính đối thủ của bạn."};
    public static final String[] training6 = new String[]{"Khi bạn bị bắn hết máu. Bạn có thể chọn mục Dùng Item từ MENU. Hãy thử bơm máu"};
    public static final String[] training7 = new String[]{"Xin chúc mừng. Bạn đã có thể tham gia cuộc chiến ngay bây giờ. Còn chờ gì nữa?"};
    public static final String[] makeFriend = new String[]{"Kết bạn", "Make friends"};
    public static final String[][] items = new String[][]{{"Phục hồi HP", "Teleport", "Bắn x2", "Di chuyển x2", "Tàng hình", "Ngưng gió", "Bom phá đất", "Lựu đạn", "Bom hủy diệt", "Tơ nhện", "HP đồng đội", "Đạn trái phá", "Túi đựng item 1", "Túi đựng item 2", "Túi đựng item 3", "Túi đựng item 3", "Đạn Lazer", "Đạn vòi rồng", "Chuột gắn bom", "Tên lửa", "Đạn xuyên đất", "Sao băng", "Mưa đạn", "Khoang đất", "Tự sát", "Bom mù", "Khoang đất 2", "UFO", "Đóng băng", "Khói độc", "Tơ nhện 2", "Bom hẹn giờ", "HP 50%", "HD 100%", "Vô hình", "Ma cà rồng", "Rock man", "Cyber girl"}};
    public static final String[] chuacoban = new String[]{"Bạn chưa có bạn"};
    public static final String[] xinchonmenu = new String[]{"Xin chọn Menu/Thêm bạn"};
    public static final String[] themtuphongcho = new String[]{"hoặc thêm từ phòng chờ."};
    public static final String[] buy = new String[]{"Mua"};
    public static final String[] price = new String[]{"Giá"};
    public static final String[] per = new String[]{"cái"};
    public static final String[] vaoxemtin = new String[]{"Bạn có muốn thoát game để vào xem tin tức không?"};
    public static final String[] paymentMethod = new String[]{"Hình thức nạp tiền"};
    public static final String[] use = new String[]{"Dùng"};
    public static final String[][] top = new String[][]{{"TOP ĐẠI GIA", "BẢNG THÀNH TÍCH", "DANH SÁCH BẠN BÈ", "DANH SÁCH MỜI BẠN"}};
    public static final String[] reply = new String[]{"Trả lời"};
    public static final String[] noMess1 = new String[]{"(không có tin đến)"};
    public static final String[] noMess2 = new String[]{"Để gửi tin đi, xin"};
    public static final String[] noMess3 = new String[]{"vào mục Bạn bè"};
    public static final String[] boss = new String[]{"chủ"};
    public static final String[] freeItem = new String[]{"Miễn phí"};
    public static final String[] having = new String[]{"Đang có"};
    public static final String[] howMuch = new String[]{"Bạn sẽ mua bao nhiêu ?"};
    public static final String[] amthanh = new String[]{"âm thanh"};
    public static final String[] vibrate = new String[]{"Rung"};
    public static final String[] graphicQuality = new String[]{"Cấu hình đồ họa"};
    public static final String[] imageQuality = new String[]{"Chất lượng ảnh"};
    public static final String[] macdinh = new String[]{"Mặc định"};
    public static final String[] khac = new String[]{"Khác"};
    public static final String[] buyCharactor = new String[]{"Bạn có muốn sở hữu nhân vật này không? Bạn đang có "};
    public static final String[] trading = new String[]{"Đang giao dịch..."};
    public static final String[] noMoney = new String[]{"Bạn chưa đủ tiền để mua, vui lòng quay lại sau."};
    public static final String[] addFriend = new String[]{"Thêm bạn"};
    public static final String[] more = new String[]{"Xem thêm"};
    public static final String[] deleteFriend = new String[]{"Xóa bạn"};
    public static final String[] gettingList = new String[]{"Lấy danh sách..."};
    public static final String[] deleting = new String[]{"Đang xóa..."};
    public static final String[] input4 = new String[]{"Bạn phải nhập ít nhất 4 ký tự"};
    public static final String[] inputName = new String[]{"Nhập tên bạn muốn thêm:"};
    public static final String[] searching = new String[]{"Đang tìm người bạn này..."};
    public static final String[] justSent = new String[]{"Bạn vừa gửi tin. Xin chờ giây lát để có thể gửi thêm một tin nữa"};
    public static final String[] hasSent = new String[]{"Đã gửi tin nhắn"};
    public static final String[] sendTo = new String[]{"Gửi đến "};
    public static final String[] sendTo2 = new String[]{" gửi đến "};
    public static final String[] sendMess = new String[]{"Nhắn tin"};
    public static final String[] invited = new String[]{"Đã mời chơi"};
    public static final String[] moichoi = new String[]{"Mời chơi"};
    public static final String[] idPlease = new String[]{"Nhập Game ID muốn đăng nhập."};
    public static final String[] passPlease = new String[]{"Nhập password để đăng nhập."};
    public static final String[] logging = new String[]{"Đang đăng nhập"};
    public static final String[] idRegPlease = new String[]{"Vui lòng nhập Game ID muốn đăng ký vào ô trên."};
    public static final String[] passRegPlease = new String[]{"Bạn phải nhập password đăng ký."};
    public static final String[] email_phoneRegPlease = new String[]{"Bạn phải nhập email hoặc số điện thoại đăng ký."};
    public static final String[] retypePass = new String[]{"Nhập lại password trên:"};
    public static final String[] notMath = new String[]{"Password bạn vừa gõ không khớp với password phía trên"};
    public static final String[] oldPass = new String[]{"Mật khẩu cũ: "};
    public static final String[] newPass = new String[]{"Mật khẩu mới: "};
    public static final String[] retypeNewPass = new String[]{"Nhập lại mật khẩu mới: "};
    public static final String[] plOldPass = new String[]{"Bạn phải nhập mật khẩu cũ."};
    public static final String[] plNewPass = new String[]{"Bạn phải nhập mật khẩu mới."};
    public static final String[] plRetypeNewPass = new String[]{"Bạn phải nhập lại mật khẩu mới"};
    public static final String[] newPassNotMath = new String[]{"Nhập sai mật khẩu mới"};
    public static final String[] deleteAll = new String[]{"Xóa hết"};
    public static final String[] newMess = new String[]{"Soạn tin mới"};
    public static final String[] toNewMess = new String[]{"Để soạn tin mới, xin vào mục Bạn Bè rồi chọn nhắn tin"};
    public static final String[] cannotKick = new String[]{"Không thể kick vì người này đã sẵn sàng"};
    public static final String[] cannotReady = new String[]{"Bạn không thể sẵn sàng do không đủ tiền"};
    public static final String[] ready = new String[]{"Sẵn sàng"};
    public static final String[] areReady = new String[]{"Đang sẵn sàng"};
    public static final String[] totalstakes = new String[]{"Số tiền"};
    public static final String[] onlyHave = new String[]{"Bạn chỉ có "};
    public static final String[] setPassed = new String[]{"Đã đặt mật khẩu"};
    public static final String[] setBoardNamefinish = new String[]{"Đã đặt xong"};
    public static final String[] notReady = new String[]{"Mọi người chưa sẵn sàng"};
    public static final String[] notEnoughMoney = new String[]{"Bạn không đủ tiền."};
    public static final String[] kocotien = new String[]{"Bạn chưa đủ tiền để mua."};
    public static final String[] loading = new String[]{"Đang bắt đầu"};
    public static final String[] thanks = new String[]{"Cám ơn bạn đã mua hàng!"};
    public static final String[] fullItem = new String[]{"Item này đã đầy."};
    public static final String[] addFriendSuccess = new String[]{"Thêm bạn thành công"};
    public static final String[] cannotaddFriend = new String[]{"Không thể thêm bạn vì đã có quá nhiều bạn."};
    public static final String[] isExist = new String[]{"Không thể thêm người này vì đã có sẵn."};
    public static final String[] connectFail = new String[]{"Kết nối thất bại, xin kiểm tra đường truyền."};
    public static final String[] cannotdelete = new String[]{"Không thể xóa người này."};
    public static final String[] serverDisconnect = new String[]{"Máy chủ tắt hoặc mất sóng."};
    public static final String[] ExistsNick = new String[]{"Tài khoản đã có người sử dụng"};
    public static final String[] sendSMS = new String[]{"Đã gửi thông tin đăng ký thành công. Xin thoát game và chờ giây lát."};
    public static final String[] sendSMSFail = new String[]{"Không thể gởi tin nhắn. Xin kiểm tra tiền và thử khởi động lại game."};
    public static final String[] notFindID = new String[]{"Không tìm thấy nick bạn vừa nhập"};
    public static final String[] them = new String[]{"Thêm"};
    public static final String[] vao = new String[]{"vào danh sách bạn bè"};
    public static final String[] autoOpen = new String[]{"Nếu điện thoại không tự mở trình duyệt, xin vui lòng tắt Mobi Army"};
    public static final String[] seriNumber = new String[]{"Số seri"};
    public static final String[] pinNumber = new String[]{"Mã số thẻ (PIN)"};
    public static final String[] inputSeri = new String[]{"Bạn phải nhập số seri"};
    public static final String[] inputPIN = new String[]{"Bạn phải nhập mã số thẻ (PIN)"};
    public static final String[] WIN = new String[]{"BẠN ĐÃ THẮNG !"};
    public static final String[] LOSE = new String[]{"BẠN ĐÃ THUA !"};
    public static final String[] RAW = new String[]{"HÒA !"};
    public static final String[] CONTINUE = new String[]{"TIẾP TỤC"};
    public static final String[] USEITEM = new String[]{"DÙNG ITEM"};
    public static final String[] VIEWMAP = new String[]{"XEM BẢN ĐỒ"};
    public static final String[] SKIP = new String[]{"BỎ LƯỢT"};
    public static final String[] LEAVEBATTLE = new String[]{"RỜI TRẬN CHIẾN"};
    public static final String[] youWillLose = new String[]{"Bạn sẽ bị xử thua, bạn có muốn thoát ra không ?"};
    public static final String[] wantExit = new String[]{"Bạn có muốn thoát ra không ?"};
    public static final String[] exitToWatchNews = new String[]{"Bạn có muốn thoát game để vào xem tin tức không ?"};
    public static final String[] sendSuccess = new String[]{"Gửi tin nhắn thành công"};
    public static final String[] sendFail = new String[]{"Gửi tin nhắn thất bại"};
    public static final String[] adding = new String[]{"Đang thêm"};
    public static final String[] starting = new String[]{"Đang bắt đầu..."};
    public static final String[] getRoomList = new String[]{"Lấy danh sách phòng"};
    public static final String[] join = new String[]{"Tham gia"};
    public static final String[] setting = new String[]{"Cài đặt..."};
    public static final String[] begin = new String[]{"Bắt đầu"};
    public static final String[] cannotLeave = new String[]{"Bạn không thể rời trận chiến khi đang sẵn sàng"};
    public static final String[] buyMoreBag = new String[]{"Xin mua thêm túi đồ tại cửa hàng."};
    public static final String[] chicothe = new String[]{"Chỉ có thể mang theo "};
    public static final String[] itemnay = new String[]{" item này"};
    public static final String[] empty = new String[]{"Số lượng item này đã hết. Xin hãy mua thêm từ của hàng."};
    public static final String[] kicked = new String[]{"Bạn bị kick bởi chủ phòng."};
    public static final String[] viewScore = new String[]{"Xem thành tích..."};
    public static final String[] win = new String[]{"Thắng"};
    public static final String[] lose = new String[]{"Thua"};
    public static final String[] raw = new String[]{"Hòa"};
    public static final String[] cantsee = new String[]{"Không thể xem thông tin"};
    public static final String[] noReady = new String[]{"Ko s.sàng"};
    public static final String[] hello = new String[]{"Xin chào, mình có thể giúp gì được cho bạn?"};
    public static final String[] see = new String[]{"Xem"};
    public static final String[] cannotsendMess = new String[]{"Không thể gửi tin nhắn. Xin soạn tin: "};
    public static final String[] forgotPass = new String[]{"Quên mật khẩu"};
    public static final String[] question = new String[]{"Câu hỏi thường gặp"};
    public static final String[] callhotline = new String[]{"Gọi hotline"};
    public static final String[] usingPhone = new String[]{"Có phải bạn đang dùng số điện thoại đăng ký tài khoản không?", "Are you using the phone number to register your account?"};
    public static final String[] usingPhone2 = new String[]{"Bạn phải dùng số điện thoại đăng ký tài khoản để lấy lại mật khẩu."};
    public static final String[] Question = new String[]{"Một số máy cần thoát game để thực hiện thao tác này. bạn muốn thoát không?"};
    public static final String[] finish = new String[]{"Đóng"};
    public static final String[] emptyArea = new String[]{"TẠO BÀN"};
    public static final String[] random = new String[]{"Ngẫu nhiên"};
    public static final String[] emptyRoom = new String[]{"CHỌN KHU VỰC"};
    public static final String[] download = new String[]{"Đang tải dữ liệu..."};
    public static final String[] shop_eq = new String[]{"TRANG BỊ"};
    public static final String[] experience = new String[]{"Kinh nghiệm"};
    public static final String[] sendMessMoney = new String[]{"Đang gửi tin nhắn nạp tiền."};
    public static final String[] sendMoneySucc = new String[]{"Đã nạp tiền xong. Xin chờ tin nhắn xác nhận. Lưu ý bạn chỉ nạp được 3 lần trong 5 phút."};
    public static final String[] SPECIAL = new String[]{"KĨ NĂNG ĐẶC BIỆT"};
    public static final String[] areYouSure = new String[]{"Bạn có chắc không ?"};
    public static final String[] remember = new String[]{"Nhớ"};
    public static final String[] detail = new String[]{"Chi tiết"};
    public static final String[] agree = new String[]{"Đồng ý"};
    public static final String[] heath = new String[]{"Sinh lực"};
    public static final String[] dam = new String[]{"Sức mạnh"};
    public static final String[] defend = new String[]{"Phòng thủ"};
    public static final String[] lucky = new String[]{"May mắn"};
    public static final String[] team = new String[]{"Đồng đội"};
    public static final String[] xu = new String[]{" xu"};
    public static final String[] luong = new String[]{" lượng"};
    public static final String[] luong2 = new String[]{"lg"};
    public static final String[] RULET = new String[]{"QUAY SỐ"};
    public static final String[] INVENTORY = new String[]{"TRANG BỊ"};
    public static final String[] sinhluc = new String[]{"Sinh lực"};
    public static final String[] sucmanh = new String[]{"Sức mạnh"};
    public static final String[] phongthu = new String[]{"Phòng thủ"};
    public static final String[] mayman = new String[]{"May mắn"};
    public static final String[] dongodi = new String[]{"Đồng đội"};
    public static final String[] All5 = new String[]{"+5% tất cả các thuộc tính"};
    public static final String[] expr = new String[]{"Ngày hết hạn"};
    public static final String[] eSlot = new String[]{"Slot trống"};
    public static final String[] chatAll = new String[]{"chat với toàn đội"};
    public static final String[] muaXu = new String[]{"Mua bằng xu"};
    public static final String[] muaLuong = new String[]{"Mua bằng lượng"};
    public static final String[] thanhvien = new String[]{"Thành viên"};
    public static final String[] doitruong = new String[]{"Đội trưởng"};
    public static final String[] ngansach = new String[]{"Ngân sách"};
    public static final String[] ngaythanhlap = new String[]{"Ngày thành lập"};
    public static final String[] clanItem = new String[]{"Vui lòng vào cửa hàng để mua item"};
    public static final String[] time = new String[]{"Thời gian"};
    public static final String[] TOPCLAN = new String[]{"TOP ĐỘI"};
    public static final String[] topClan = new String[]{"Top đội"};
    public static final String[] ruongdo = new String[]{"Rương đồ"};
    public static final String[] chuacodo = new String[]{"Chưa có đồ trong rương"};
    public static final String[] trangbi = new String[]{"Trang bị"};
    public static final String[] kethop = new String[]{"Sử dụng"};
    public static final String[] xacnhan = new String[]{"Xác nhận"};
    public static final String[] luutrangthai = new String[]{"Bạn có muốn lưu trạng thái này không ?"};
    public static final String[] banphaitren = new String[]{"Bạn phải trên level "};
    public static final String[] moicothe = new String[]{" mới có thể sử dụng trạng bị này"};
    public static final String[] nhapsoluong = new String[]{"Nhập số lượng"};
    public static final String[] diemdongdoi = new String[]{" điểm đồng đội"};
    public static final String[] battaticon = new String[]{"BẬT/TẮT ICON ĐỘI"};
    public static final String[] lamlai = new String[]{"Làm lại"};
    public static final String[] THANHVIENDOI = new String[]{"THÀNH VIÊN ĐỘI"};
    public static final String[] xoadulieu = new String[]{"Xóa dữ liệu tạm"};
    public static final String[] quay = new String[]{"Quay"};
    public static final String[] muctien = new String[]{"Mức tiền"};
    public static final String[] bancomuon = new String[]{"Bạn có muốn chơi quay số với giá "};
    public static final String[] xinchucmung = new String[]{"Xin chúc mừng, phần thưởng của bạn là "};
    public static final String[] lansau = new String[]{"Chúc may mắn lần sau"};
    public static final String[] NANGCAP = new String[]{"NÂNG CẤP"};
    public static final String[] QUAYSO = new String[]{"QUAY SỐ"};
    public static final String[] DODACBIET = new String[]{"ĐỒ ĐẶC BIỆT"};
    public static final String[] CUAHANG = new String[]{"CỬA HÀNG"};
    public static final String[] BIETDOI = new String[]{"BIỆT ĐỘI"};
    public static final String[] BANGDANHDU = new String[]{"BẢNG DANH DỰ"};
    public static final String[] chuacodoi = new String[]{"Bạn chưa có đội, bạn có muốn đăng kí vào đội không"};
    public static final String[] shoptrangbi = new String[]{"Shop trang bị"};
    public static final String[] bancochac = new String[]{"Bạn có chắc muốn mua với giá "};
    public static final String[] cuahang = new String[]{"Cửa hàng"};
    public static final String[] muasudung = new String[]{"Bạn có chắc muốn mua và sử dụng item này không ?"};
    public static final String[] dathang = new String[]{"Đặt hàng"};
    public static final String[] ngay = new String[]{"ngày"};
    public static final String[] gio = new String[]{"giờ"};
    public static final String[] shopDacBiet = new String[]{"Shop đặc biệt"};
    public static final String[] bossbattle = new String[]{"ĐẤU TRÙM"};
    public static final String[] chonItem = new String[]{"Chọn item"};
    public static final String[] giaodich = new String[]{"Giao dịch thành công."};
    public static final String[] chooseServer = new String[]{"Chọn máy chủ"};
    public static final String[] MISSION = new String[]{"NHIỆM VỤ"};
    public static final String[] mission = new String[]{"Nhiệm vụ"};
    public static final String[] nhanthuong = new String[]{"Nhận thưởng"};
    public static final String[] ITEM_DOI = new String[]{"ITEM ĐỘI"};
    public static final String[] topCaoThuTuan = new String[]{"CAO THỦ TUẦN"};
    public static final String[] topXuTuan = new String[]{"ĐẠI GIA TUẦN"};
    public static final String[] topCaothu = new String[]{"CAO THỦ"};
    public static final String[] topDaigiaXu = new String[]{"ĐẠI GIA XU"};
    public static final String[] topDaigiaLuong = new String[]{"ĐẠI GIA LƯỢNG"};
    public static final String[] chonmaychu = new String[]{"Chọn máy chủ"};
    public static final String[] fomula = new String[]{"Công thức"};
    public static final String[] levelRequire = new String[]{"Level yêu cầu"};
    public static final String[] conthucchetao = new String[]{"CÔNG THỨC CHẾ TẠO"};
    public static final String[] congthuccap = new String[]{"Công thức cấp"};
    public static final String[] chedo = new String[]{"Chế đồ"};
    public static final String[] goptienXu = new String[]{"Góp tiền xu"};
    public static final String[] goptienLuong = new String[]{"Góp tiền lượng"};
    public static final String[] nhapsoxu = new String[]{"Nhập số xu đóng góp"};
    public static final String[] nhapsoLuong = new String[]{"Nhập số lượng đóng góp"};
    public static final String[] phanthuong = new String[]{"Phần thưởng"};
    public static final String[] windAngle = new String[]{"Gió góc"};
    public static final String[] findArea = new String[]{"Tìm khu vực"};
    public static final String[] nhapSoPhong = new String[]{"Nhập số phòng"};
    public static final String[] nhapKhuVuc = new String[]{"Nhập khu vực"};
    public static final String[] dienDan = new String[]{"DIỄN ĐÀN"};
    public static final String[] notEmpty = new String[]{"Không được để trống"};
    public static final String[] banthan = new String[]{"BẢN THÂN"};
    public static final String[] noticGiahanTrangBi = new String[]{"Trang bị của bạn đã hết hạn. Xin hãy gia hạn!"};
    public static final String[] disconnect = new String[]{"Mất kết nối"};
    public static final String[] updateServer = new String[]{"Bản cập nhật mới nhất"};
    public static final String[] cup = new String[]{"Cúp"};
    public static final String[] dangKyGam = new String[]{"Mời bạn vào web để đăng kí"};
    public static final String[] dangkyFail = new String[]{"Đăng kí không thành công"};
    public static final String[] dangkySucceed = new String[]{"Đăng kí thành công"};
    public static final String[] clanSize = new String[]{"Chưa có clan"};
    public static final String[] missions = new String[]{"Nhiệm vụ"};
    public static final String[] missionComplete = new String[]{"Hoàn thành"};
    public static final String[] noTimeRespond = new String[]{"Không có phản hồi từ Server"};
    public static final String[] createZone = new String[]{"Tạo khu vực"};
    public static final String[] createCharName = new String[]{"Nhập vào tên tài khoản"};
    public static final String[] reConnect = new String[]{"Kết nối mạng không ổn định. Bạn có muốn chờ!"};
    public static final String[] backVersion = new String[]{"Quay lại"};
    public static final String[] deletedFriendSc = new String[]{"Xóa bạn"};
    public static final String[][] quality = new String[][]{{"Cao", "Vừa", "Thấp"}, {"High", "Medium", "Low"}};
    public static final String[] cameraMode = new String[]{"Chế độ Camera"};

    public static final String backVersion() {
        return backVersion[language];
    }

    public static final String reConnect() {
        return reConnect[language];
    }

    public static final String createCharName() {
        return createCharName[language];
    }

    public static final String dangkyFail() {
        return dangkyFail[language];
    }

    public static final String dangkySucceed() {
        return dangkySucceed[language];
    }

    public static final String createZone() {
        return createZone[language];
    }

    public static final String noTimeRespond() {
        return noTimeRespond[language];
    }

    public static final String missionComplete() {
        return missionComplete[language];
    }

    public static final String missions() {
        return missions[language];
    }

    public static final String dangKyGam() {
        return dangKyGam[language];
    }

    public static final String clanSize() {
        return clanSize[language];
    }

    public static final String cup() {
        return cup[language];
    }

    public static final String updateServer() {
        return updateServer[language];
    }

    public static final String disconnect() {
        return disconnect[language];
    }

    public static final String noticGiahanTrangBi() {
        return noticGiahanTrangBi[language];
    }

    public static final String banthan() {
        return banthan[language];
    }

    public static final String notEmpty() {
        return notEmpty[language];
    }

    public static final String dienDan() {
        return dienDan[language];
    }

    public static final String nhapKhuVuc() {
        return nhapKhuVuc[language];
    }

    public static final String nhapSoPhong() {
        return nhapSoPhong[language];
    }

    public static final String findArea() {
        return findArea[language];
    }

    public static final String windAngle() {
        return windAngle[language];
    }

    public static final String phanthuong() {
        return phanthuong[language];
    }

    public static final String nhapsoxu() {
        return nhapsoxu[language];
    }

    public static final String nhapsoLuong() {
        return nhapsoLuong[language];
    }

    public static final String goptienXu() {
        return goptienXu[language];
    }

    public static final String goptienLuong() {
        return goptienLuong[language];
    }

    public static final String chedo() {
        return chedo[language];
    }

    public static final String congthuccap() {
        return congthuccap[language];
    }

    public static final String congthucchetao() {
        return conthucchetao[language];
    }

    public static final String levelRequire() {
        return levelRequire[language];
    }

    public static final String fomula() {
        return fomula[language];
    }

    public static final String chonmaychu() {
        return chonmaychu[language];
    }

    public static final String topCaothuTuan() {
        return topCaoThuTuan[language];
    }

    public static final String topXuTuan() {
        return topXuTuan[language];
    }

    public static final String topCaothu() {
        return topCaothu[language];
    }

    public static final String topDaiGiaXu() {
        return topDaigiaXu[language];
    }

    public static final String topDaigiaLuong() {
        return topDaigiaLuong[language];
    }

    public static final String ITEM_DOI() {
        return ITEM_DOI[language];
    }

    public static final String nhanthuong() {
        return nhanthuong[language];
    }

    public static final String mission() {
        return mission[language];
    }

    public static final String MISSION() {
        return MISSION[language];
    }

    public static final String chooseServer() {
        return chooseServer[language];
    }

    public static final String giaodich() {
        return giaodich[language];
    }

    public static final String chonItem() {
        return chonItem[language];
    }

    public static final String shoDacBiet() {
        return shopDacBiet[language];
    }

    public static final String gio() {
        return gio[language];
    }

    public static final String ngay() {
        return ngay[language];
    }

    public static final String dathang() {
        return dathang[language];
    }

    public static final String muavasudung() {
        return muasudung[language];
    }

    public static final String cuahang() {
        return cuahang[language];
    }

    public static final String bancochac() {
        return bancochac[language];
    }

    public static final String shoptrangbi() {
        return shoptrangbi[language];
    }

    public static final String chuacodoi() {
        return chuacodoi[language];
    }

    public static final String BANGDANHDU() {
        return BANGDANHDU[language];
    }

    public static final String CUAHANG() {
        return CUAHANG[language];
    }

    public static final String BIETDOI() {
        return BIETDOI[language];
    }

    public static final String DODACBIET() {
        return DODACBIET[language];
    }

    public static final String QUAYSO() {
        return QUAYSO[language];
    }

    public static final String NANGCAP() {
        return NANGCAP[language];
    }

    public static final String lansau() {
        return lansau[language];
    }

    public static final String xinchucmung() {
        return xinchucmung[language];
    }

    public static final String bancomuon() {
        return bancomuon[language];
    }

    public static final String muctien() {
        return muctien[language];
    }

    public static final String quay() {
        return quay[language];
    }

    public static final String xoadulieu() {
        return xoadulieu[language];
    }

    public static final String THANHVIENDOI() {
        return THANHVIENDOI[language];
    }

    public static final String lamlai() {
        return lamlai[language];
    }

    public static final String battaticon() {
        return battaticon[language];
    }

    public static final String diemdongdoi() {
        return diemdongdoi[language];
    }

    public static final String nhapsoluong() {
        return nhapsoluong[language];
    }

    public static final String banphaitren() {
        return banphaitren[language];
    }

    public static final String moicothe() {
        return moicothe[language];
    }

    public static final String saveEquip() {
        return luutrangthai[language];
    }

    public static final String xacnhan() {
        return xacnhan[language];
    }

    public static final String trangbi() {
        return trangbi[language];
    }

    public static final String kethop() {
        return kethop[language];
    }

    public static final String beNotInventory() {
        return chuacodo[language];
    }

    public static final String ruongdo() {
        return ruongdo[language];
    }

    public static final String topClan() {
        return topClan[language];
    }

    public static final String TOPCLAN() {
        return TOPCLAN[language];
    }

    public static final String time() {
        return time[language];
    }

    public static final String clanItem() {
        return clanItem[language];
    }

    public static final String doitruong() {
        return doitruong[language];
    }

    public static final String ngansach() {
        return ngansach[language];
    }

    public static final String ngaythanhlap() {
        return ngaythanhlap[language];
    }

    public static final String thanhvien() {
        return thanhvien[language];
    }

    public static final String muaLuong() {
        return muaLuong[language];
    }

    public static final String muaXu() {
        return muaXu[language];
    }

    public static final String chatAll() {
        return chatAll[language];
    }

    public static final String eSlot() {
        return eSlot[language];
    }

    public static final String expr() {
        return expr[language];
    }

    public static final String All5() {
        return All5[language];
    }

    public static final String sinhluc() {
        return sinhluc[language];
    }

    public static final String sucmanh() {
        return sucmanh[language];
    }

    public static final String phongthu() {
        return phongthu[language];
    }

    public static final String mayman() {
        return mayman[language];
    }

    public static final String dongdoi() {
        return dongodi[language];
    }

    public static final String INVENTORY() {
        return INVENTORY[language];
    }

    public static final String RULET() {
        return RULET[language];
    }

    public static final String luong2() {
        return luong2[language];
    }

    public static final String xu() {
        return xu[language];
    }

    public static final String luong() {
        return luong[language];
    }

    public static final String heath() {
        return heath[language];
    }

    public static final String dam() {
        return dam[language];
    }

    public static final String defend() {
        return defend[language];
    }

    public static final String lucky() {
        return lucky[language];
    }

    public static final String team() {
        return team[language];
    }

    public static final String agree() {
        return agree[language];
    }

    public static final String detail() {
        return detail[language];
    }

    public static final String SPECIAL() {
        return SPECIAL[language];
    }

    public static final String remember() {
        return remember[language];
    }

    public static final String areYouSure() {
        return areYouSure[language];
    }

    public static final String bossbattle() {
        return bossbattle[language];
    }

    public static final String download() {
        return download[language];
    }

    public static final String random() {
        return random[language];
    }

    public static final String emptyArea() {
        return emptyArea[language];
    }

    public static final String finish() {
        return finish[language];
    }

    public static final String Question() {
        return Question[language];
    }

    public static final String usingPhone2() {
        return usingPhone2[language];
    }

    public static final String usingPhone() {
        return usingPhone[language];
    }

    public static final String callhotline() {
        return callhotline[language];
    }

    public static final String question() {
        return question[language];
    }

    public static final String forgotPass() {
        return forgotPass[language];
    }

    public static final String cannotsendMess() {
        return cannotsendMess[language];
    }

    public static final String see() {
        return see[language];
    }

    public static final String helo() {
        return hello[language];
    }

    public static final String RAW() {
        return RAW[language];
    }

    public static final String noReady() {
        return noReady[language];
    }

    public static final String cantsee() {
        return cantsee[language];
    }

    public static final String raw() {
        return raw[language];
    }

    public static final String lose() {
        return lose[language];
    }

    public static final String win() {
        return win[language];
    }

    public static final String viewScore() {
        return viewScore[language];
    }

    public static final String kicked() {
        return kicked[language];
    }

    public static final String empty() {
        return empty[language];
    }

    public static final String itemnay() {
        return itemnay[language];
    }

    public static final String chicothe() {
        return chicothe[language];
    }

    public static final String buyMoreBag() {
        return buyMoreBag[language];
    }

    public static final String cannotLeave() {
        return cannotLeave[language];
    }

    public static final String begin() {
        return begin[language];
    }

    public static final String setting() {
        return setting[language];
    }

    public static final String join() {
        return join[language];
    }

    public static final String getRoomlist() {
        return getRoomList[language];
    }

    public static final String starting() {
        return starting[language];
    }

    public static final String adding() {
        return adding[language];
    }

    public static final String sendSuccess() {
        return sendSuccess[language];
    }

    public static final String sendFail() {
        return sendFail[language];
    }

    public static final String exitToWatchNews() {
        return exitToWatchNews[language];
    }

    public static final String wantExit() {
        return wantExit[language];
    }

    public static final String youWillLose() {
        return youWillLose[language];
    }

    public static final String CONTINUE() {
        return CONTINUE[language];
    }

    public static String USEITEM() {
        return USEITEM[language];
    }

    public static String LEAVEBATTLE() {
        return LEAVEBATTLE[language];
    }

    public static String SKIP() {
        return SKIP[language];
    }

    public static String VIEWMAP() {
        return VIEWMAP[language];
    }

    public static final String WIN() {
        return WIN[language];
    }

    public static final String LOSE() {
        return LOSE[language];
    }

    public static final String inputPin() {
        return inputPIN[language];
    }

    public static final String inputSeri() {
        return inputSeri[language];
    }

    public static final String pinNumber() {
        return pinNumber[language];
    }

    public static final String seriNumber() {
        return seriNumber[language];
    }

    public static final String sendMoneySucc() {
        return sendMoneySucc[language];
    }

    public static final String sendMessMoney() {
        return sendMessMoney[language];
    }

    public static final String autoOpen() {
        return autoOpen[language];
    }

    public static final String them() {
        return them[language];
    }

    public static final String vao() {
        return vao[language];
    }

    public static final String notFindID() {
        return notFindID[language];
    }

    public static final String sendSMSFail() {
        return sendSMS[language];
    }

    public static final String sendSMṢ() {
        return sendSuccess[language];
    }

    public static final String ExistsNick() {
        return ExistsNick[language];
    }

    public static final String serverDis() {
        return serverDisconnect[language];
    }

    public static final String cannotdelete() {
        return cannotdelete[language];
    }

    public static final String deleteFriendSc() {
        return deletedFriendSc[language];
    }

    public static final String connectFail() {
        return connectFail[language];
    }

    public static final String isExist() {
        return isExist[language];
    }

    public static final String cannotaddFriend() {
        return cannotaddFriend[language];
    }

    public static final String addFriendSuccess() {
        return addFriendSuccess[language];
    }

    public static final String fullItem() {
        return fullItem[language];
    }

    public static final String thanks() {
        return thanks[language];
    }

    public static final String kocotien() {
        return kocotien[language];
    }

    public static final String loading() {
        return loading[language];
    }

    public static final String notEnoughMoney() {
        return notEnoughMoney[language];
    }

    public static final String notReady() {
        return notReady[language];
    }

    public static final String setBoardNamefinish() {
        return setBoardNamefinish[language];
    }

    public static final String setPassed() {
        return setPassed[language];
    }

    public static final String onlyHave() {
        return onlyHave[language];
    }

    public static final String totalstakes() {
        return totalstakes[language];
    }

    public static final String ready() {
        return ready[language];
    }

    public static final String areReady() {
        return ready[language];
    }

    public static final String cannotReady() {
        return cannotReady[language];
    }

    public static final String cannotKick() {
        return cannotKick[language];
    }

    public static final String toNewMess() {
        return toNewMess[language];
    }

    public static final String newMess() {
        return newMess[language];
    }

    public static final String deleteAll() {
        return deleteAll[language];
    }

    public static final String plOldPass() {
        return plOldPass[language];
    }

    public static final String plNewPass() {
        return plNewPass[language];
    }

    public static final String plRetypeNewPass() {
        return plRetypeNewPass[language];
    }

    public static final String newPassNotMath() {
        return newPassNotMath[language];
    }

    public static final String oldPass() {
        return oldPass[language];
    }

    public static final String newPass() {
        return newPass[language];
    }

    public static final String retypeNewPass() {
        return retypeNewPass[language];
    }

    public static final String idPlease() {
        return idPlease[language];
    }

    public static final String notMath() {
        return notMath[language];
    }

    public static final String retypePass() {
        return retypePass[language];
    }

    public static final String passRegPlease() {
        return passRegPlease[language];
    }

    public static final String email_phoneRegPlease() {
        return email_phoneRegPlease[language];
    }

    public static final String idRegPlease() {
        return idRegPlease[language];
    }

    public static final String logging() {
        return logging[language];
    }

    public static final String passPlease() {
        return passPlease[language];
    }

    public static final String moichoi() {
        return moichoi[language];
    }

    public static final String invited() {
        return invited[language];
    }

    public static final String sendMess() {
        return sendMess[language];
    }

    public static final String hasSent() {
        return hasSent[language];
    }

    public static final String sendTo() {
        return sendTo[language];
    }

    public static final String sendTo2() {
        return sendTo2[language];
    }

    public static final String justSent() {
        return justSent[language];
    }

    public static final String searching() {
        return searching[language];
    }

    public static final String inputName() {
        return inputName[language];
    }

    public static final String input4() {
        return input4[language];
    }

    public static final String deleting() {
        return deleting[language];
    }

    public static final String gettingList() {
        return gettingList[language];
    }

    public static final String addFriend() {
        return addFriend[language];
    }

    public static final String more() {
        return more[language];
    }

    public static final String deleteFriend() {
        return deleteFriend[language];
    }

    public static final String noMoney() {
        return noMoney[language];
    }

    public static final String trading() {
        return trading[language];
    }

    public static final String buyCharactor() {
        return buyCharactor[language];
    }

    public static final String payMethod() {
        return paymentMethod[language];
    }

    public static final String no() {
        return no[language];
    }

    public static final String yes() {
        return yes[language];
    }

    public static final String[] quality() {
        return quality[language];
    }

    public static final String macdinh() {
        return macdinh[language];
    }

    public static final String khac() {
        return khac[language];
    }

    public static final String imageQuality() {
        return imageQuality[language];
    }

    public static final String graphicQuality() {
        return graphicQuality[language];
    }

    public static final String vibrate() {
        return vibrate[language];
    }

    public static final String amthanh() {
        return amthanh[language];
    }

    public static final String howMuch() {
        return howMuch[language];
    }

    public static final String having() {
        return having[language];
    }

    public static final String freeItem() {
        return freeItem[language];
    }

    public static final String boss() {
        return boss[language];
    }

    public static final String noMess1() {
        return noMess1[language];
    }

    public static final String noMess2() {
        return noMess2[language];
    }

    public static final String noMess3() {
        return noMess3[language];
    }

    public static final String reply() {
        return reply[language];
    }

    public static final String changePass() {
        return change_pass[language];
    }

    public static final String otherGame() {
        return otherGame[language];
    }

    public static final String MESS() {
        return MESS[language];
    }

    public static final String achievement() {
        return achievement[language];
    }

    public static final String FRIEND() {
        return FRIEND[language];
    }

    public static final String topScore() {
        return topScore[language];
    }

    public static final String event() {
        return event[language];
    }

    public static final String soundOff() {
        return soundOff[language];
    }

    public static final String[] top() {
        return top[language];
    }

    public static final String experience() {
        return experience[language];
    }

    public static final String use() {
        return use[language];
    }

    public static final String vaoxemtin() {
        return vaoxemtin[language];
    }

    public static final String buy() {
        return buy[language];
    }

    public static final String price() {
        return price[language];
    }

    public static final String per() {
        return per[language];
    }

    public static final String cameraMode() {
        return cameraMode[language];
    }

    public static final String chuacoban() {
        return chuacoban[language];
    }

    public static final String xinchonmenu() {
        return xinchonmenu[language];
    }

    public static final String themtuphongcho() {
        return themtuphongcho[language];
    }

    public static final String[] items() {
        return items[language];
    }

    public static final String makeFriend() {
        return makeFriend[language];
    }

    public static final String training1() {
        return training1[language];
    }

    public static final String training2() {
        return training2[language];
    }

    public static final String trainin3() {
        return training3[language];
    }

    public static final String training4() {
        return training4[language];
    }

    public static final String training5() {
        return training5[language];
    }

    public static final String training6() {
        return training6[language];
    }

    public static final String training7() {
        return training7[language];
    }

    public static final String windEffect() {
        return windEffect[language];
    }

    public static final String damage() {
        return damage[language];
    }

    public static final String bulletNumber() {
        return bulletNumber[language];
    }

    public static final String setBoardName() {
        return setBoardName[language];
    }

    public static final String setPerson() {
        return setPerson[language];
    }

    public static final String setMoney() {
        return setMoney[language];
    }

    public static final String setPass() {
        return setPass[language];
    }

    public static String sound() {
        return sound[language];
    }

    public static String soundOn() {
        return soundOn[language];
    }

    public static String forward() {
        return forward[language];
    }

    public static String reg() {
        return reg[language];
    }

    public static String signIn() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        StackTraceElement caller = stackTrace[2];
        System.out.println("Called by: " + caller);
        return signIn[language];
    }

    public static String delete() {
        return delete[language];
    }

    public static String id() {
        return id[language];
    }

    public static String pass() {
        return pass[language];
    }

    public static String email_phone() {
        return email_phone[language];
    }

    public static String getNewID1() {
        return getNewID1[language];
    }

    public static String getNewID2() {
        return getNewID2[language];
    }

    public static String connecting() {
        return connecting[language];
    }

    public static String pleaseWait() {
        return pleaseWait[language];
    }

    public static String invite() {
        return invite[language];
    }

    public static String startGame() {
        return startGame[language];
    }

    public static String shop() {
        return shop[language];
    }

    public static String shop_eq() {
        return shop_eq[language];
    }

    public static String news() {
        return news[language];
    }

    public static String charge() {
        return charge[language];
    }

    public static String charge2() {
        return charge2[language];
    }

    public static String information() {
        return information[language];
    }

    public static String option() {
        return option[language];
    }

    public static String playnow() {
        return playNow[language];
    }

    public static String toArea() {
        return toarea[language];
    }

    public static String selectCharactor() {
        return playerSelect[language];
    }

    public static String training() {
        return training[language];
    }

    public static String name() {
        return name[language];
    }

    public static String money() {
        return money[language];
    }

    public static String select() {
        return select[language];
    }

    public static String exit() {
        return exit[language];
    }

    public static String back() {
        return back[language];
    }

    public static String close() {
        return close[language];
    }

    public static String update() {
        return update[language];
    }

    public static String enter() {
        return enter[language];
    }

    public static String room() {
        return room[language];
    }

    public static String ROOM() {
        return ROOM[language];
    }

    public static String area() {
        return area[language];
    }

    public static String battleArea() {
        return battleArea[language];
    }

    public static String selectMap() {
        return selectMap[language];
    }

    public static String findFriend() {
        return findFriend[language];
    }

    public static String changeTeam() {
        return changeTeam[language];
    }

    public static String friends() {
        return friend[language];
    }

    public static String message() {
        return mess[language];
    }

    public static String leaveBattle() {
        return leaveBattle[language];
    }

    public static String emptyRoom() {
        return emptyRoom[language];
    }
}
