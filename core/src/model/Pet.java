package model;

import java.util.HashMap;

import CLib.RMS;
import CLib.mImage;
import network.GameService;

public class Pet {
    public String name;
    public byte level;
    public float percernt;
    public int frameStartStand;
    public int frameEndStand;
    public int frameStartAttack;
    public int frameEndAttack;
    public int dxMove;
    public int dyMove;
    public int dxAtk;
    public int dyAtk;
    public byte tickMove;
    public byte tickAtk;

    public Skill[] skills;
    public HashMap<Integer, mImage> imgPetMap = new HashMap<>();

    public void addIcon(int idMIcon, byte[] dataMIcon, short lenMIcon) {
        if (this.imgPetMap == null) {
            return;
        }
        this.imgPetMap.put(idMIcon, mImage.createImage(dataMIcon, 0, lenMIcon));
        try {
            RMS.saveRMS(this.name + "_" + idMIcon, dataMIcon);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public mImage getImage(int imageId) {
        mImage img = imgPetMap.get(imageId);
        if (img == null) {
            byte[] dataMIcon = RMS.loadRMS(this.name + "_" + imageId);
            if (dataMIcon != null) {
                img = mImage.createImage(dataMIcon, 0, dataMIcon.length);
                imgPetMap.put(imageId, img);
            } else {
                GameService.gI().getMaterialIcon((byte) 5, imageId, -1, this.name);
            }
        }
        return img;
    }

}
