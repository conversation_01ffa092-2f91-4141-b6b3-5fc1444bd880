package model;

import CLib.mImage;
import network.GameService;

import java.util.HashMap;

/**
 * Created by khiem on 9/8/2025.
 *
 * <AUTHOR>
 */
public class BossNew {
    public static HashMap<Byte, BossNew> hashBoss = new HashMap<>();
    
    // Background loading threads
    private static Thread backgroundLoadingThread;
    private static boolean isBackgroundLoading = false;
    
    public int id;
    public String name;
    public int[][] standing;
    public int[][] walking;
    public int[][] hit;
    public int[][] attack;
    public int[][] death;
    public int width;
    public int height;
    public mImage[] images;
    public int dx;
    public int dy;
    public int tick;
    
    // Image cache để lưu ảnh trong memory
    private HashMap<Integer, mImage> imageCache = new HashMap<>();
    
    // Loading state
    private boolean isLoading = false;
    private boolean isFullyLoaded = false;

    /**
     * Load images immediately (blocking)
     */
    public void loadImages() {
        if (isLoading || isFullyLoaded) return;
        
        int totalLength = 0;
        if (standing != null) totalLength += standing.length;
        if (walking != null) totalLength += walking.length;
        if (hit != null) totalLength += hit.length;
        if (attack != null) totalLength += attack.length;
        if (death != null) totalLength += death.length;
        
        images = new mImage[totalLength];
        
        // Load standing images
        if (standing != null) {
            for (int i = 0; i < standing.length; i++) {
                if (standing[i] != null && standing[i].length >= 2) {
                    for (int j = standing[i][0]; j <= standing[i][1]; j++) {
                        requestImageIfNotExists(j);
                    }
                }
            }
        }
        
        // Load walking images
        if (walking != null) {
            for (int i = 0; i < walking.length; i++) {
                if (walking[i] != null && walking[i].length >= 2) {
                    for (int j = walking[i][0]; j <= walking[i][1]; j++) {
                        requestImageIfNotExists(j);
                    }
                }
            }
        }
        
        // Load hit images
        if (hit != null) {
            for (int i = 0; i < hit.length; i++) {
                if (hit[i] != null && hit[i].length >= 2) {
                    for (int j = hit[i][0]; j <= hit[i][1]; j++) {
                        requestImageIfNotExists(j);
                    }
                }
            }
        }
        
        // Load attack images
        if (attack != null) {
            for (int i = 0; i < attack.length; i++) {
                if (attack[i] != null && attack[i].length >= 2) {
                    for (int j = attack[i][0]; j <= attack[i][1]; j++) {
                        requestImageIfNotExists(j);
                    }
                }
            }
        }
        
        // Load death images
        if (death != null) {
            for (int i = 0; i < death.length; i++) {
                if (death[i] != null && death[i].length >= 2) {
                    for (int j = death[i][0]; j <= death[i][1]; j++) {
                        requestImageIfNotExists(j);
                    }
                }
            }
        }
        
        isFullyLoaded = true;
    }
    
    /**
     * Load images in background thread (non-blocking)
     */
    public void loadImagesAsync() {
        if (isLoading || isFullyLoaded) return;
        
        isLoading = true;
        Thread loadThread = new Thread(new Runnable() {
            public void run() {
                try {
                    loadImages();
                    isLoading = false;
                    model.CRes.out("Background loaded boss images for: " + name);
                } catch (Exception e) {
                    isLoading = false;
                    e.printStackTrace();
                }
            }
        });
        loadThread.setDaemon(true);
        loadThread.start();
    }
    
    /**
     * Load only essential images (standing) immediately, rest in background
     */
    public void loadEssentialImages() {
        if (isLoading) return;
        
        // Load standing images immediately (essential for display)
        if (standing != null) {
            for (int i = 0; i < standing.length; i++) {
                if (standing[i] != null && standing[i].length >= 2) {
                    for (int j = standing[i][0]; j <= standing[i][1]; j++) {
                        requestImageIfNotExists(j);
                    }
                }
            }
        }
        
        // Load other animations in background
        loadOtherImagesAsync();
    }
    
    private void loadOtherImagesAsync() {
        if (isLoading) return;
        
        isLoading = true;
        Thread loadThread = new Thread(new Runnable() {
            public void run() {
                try {
                    // Load walking images
                    if (walking != null) {
                        for (int i = 0; i < walking.length; i++) {
                            if (walking[i] != null && walking[i].length >= 2) {
                                for (int j = walking[i][0]; j <= walking[i][1]; j++) {
                                    requestImageIfNotExists(j);
                                }
                            }
                        }
                    }
                    
                    // Load hit images
                    if (hit != null) {
                        for (int i = 0; i < hit.length; i++) {
                            if (hit[i] != null && hit[i].length >= 2) {
                                for (int j = hit[i][0]; j <= hit[i][1]; j++) {
                                    requestImageIfNotExists(j);
                                }
                            }
                        }
                    }
                    
                    // Load attack images
                    if (attack != null) {
                        for (int i = 0; i < attack.length; i++) {
                            if (attack[i] != null && attack[i].length >= 2) {
                                for (int j = attack[i][0]; j <= attack[i][1]; j++) {
                                    requestImageIfNotExists(j);
                                }
                            }
                        }
                    }
                    
                    // Load death images
                    if (death != null) {
                        for (int i = 0; i < death.length; i++) {
                            if (death[i] != null && death[i].length >= 2) {
                                for (int j = death[i][0]; j <= death[i][1]; j++) {
                                    requestImageIfNotExists(j);
                                }
                            }
                        }
                    }
                    
                    isLoading = false;
                    isFullyLoaded = true;
                    model.CRes.out("Background loaded all boss images for: " + name);
                } catch (Exception e) {
                    isLoading = false;
                    e.printStackTrace();
                }
            }
        });
        loadThread.setDaemon(true);
        loadThread.start();
    }
    
    private void requestImageIfNotExists(int imageId) {
        // Check memory cache first
        if (imageCache.containsKey(imageId)) {
            return; // Already in memory cache
        }
        
        String fileName = "boss_" + name + "_" + imageId;
        byte[] existingData = model.CRes.loadRMSData(fileName);
        
        if (existingData == null) {
            // File doesn't exist, request from server
            GameService.gI().requestBossImage(name, imageId);
        } else {
            // File exists, load to memory cache
            mImage img = mImage.createImage(existingData, 0, existingData.length, "");
            imageCache.put(imageId, img);
        }
    }
    
    /**
     * Get boss image by ID from memory cache
     * @param imageId the image ID to load
     * @return mImage object or null if not found
     */
    public mImage getBossImage(int imageId) {
        // Check memory cache first
        if (imageCache.containsKey(imageId)) {
            return imageCache.get(imageId);
        }
        
        // If not in cache, try to load from file and cache it
        String fileName = "boss_" + name + "_" + imageId;
        byte[] imageData = model.CRes.loadRMSData(fileName);
        
        if (imageData != null) {
            mImage img = mImage.createImage(imageData, 0, imageData.length, "");
            imageCache.put(imageId, img); // Cache for future use
            return img;
        }
        return null;
    }
    
    /**
     * Check if boss image exists in memory cache or file
     * @param imageId the image ID to check
     * @return true if exists, false otherwise
     */
    public boolean hasImage(int imageId) {
        // Check memory cache first
        if (imageCache.containsKey(imageId)) {
            return true;
        }
        
        // Check file system
        String fileName = "boss_" + name + "_" + imageId;
        byte[] imageData = model.CRes.loadRMSData(fileName);
        return imageData != null;
    }
    
    /**
     * Add image to memory cache (called when receiving from server)
     * @param imageId the image ID
     * @param imageData the image data bytes
     */
    public void cacheImage(int imageId, byte[] imageData) {
        if (imageData != null) {
            mImage img = mImage.createImage(imageData, 0, imageData.length, "");
            imageCache.put(imageId, img);
        }
    }
    
    /**
     * Check if image is in memory cache
     * @param imageId the image ID to check
     * @return true if in memory cache
     */
    public boolean isImageCached(int imageId) {
        return imageCache.containsKey(imageId);
    }
    
    /**
     * Clear all cached images from memory
     */
    public void clearImageCache() {
        imageCache.clear();
    }
    
    /**
     * Get cache size
     * @return number of images in cache
     */
    public int getCacheSize() {
        return imageCache.size();
    }
    
    /**
     * Preload all existing images into memory cache
     */
    public void preloadAllImages() {
        // Preload standing images
        for (int i = 0; i < standing.length; i++) {
            for (int j = standing[i][0]; j <= standing[i][1]; j++) {
                preloadImage(j);
            }
        }
        
        // Preload walking images
        for (int i = 0; i < walking.length; i++) {
            for (int j = walking[i][0]; j <= walking[i][1]; j++) {
                preloadImage(j);
            }
        }
        
        // Preload hit images
        for (int i = 0; i < hit.length; i++) {
            for (int j = hit[i][0]; j <= hit[i][1]; j++) {
                preloadImage(j);
            }
        }
        
        // Preload attack images
        for (int i = 0; i < attack.length; i++) {
            for (int j = attack[i][0]; j <= attack[i][1]; j++) {
                preloadImage(j);
            }
        }
        
        // Preload death images
        for (int i = 0; i < death.length; i++) {
            for (int j = death[i][0]; j <= death[i][1]; j++) {
                preloadImage(j);
            }
        }
    }
    
    private void preloadImage(int imageId) {
        // Skip if already in cache
        if (imageCache.containsKey(imageId)) {
            return;
        }
        
        // Load from file if exists
        String fileName = "boss_" + name + "_" + imageId;
        byte[] imageData = model.CRes.loadRMSData(fileName);
        
        if (imageData != null) {
            mImage img = mImage.createImage(imageData, 0, imageData.length, "");
            imageCache.put(imageId, img);
        }
    }
    
    /**
     * Check if boss is currently loading images
     */
    public boolean isLoading() {
        return isLoading;
    }
    
    /**
     * Check if all images are loaded
     */
    public boolean isFullyLoaded() {
        return isFullyLoaded;
    }
    
    /**
     * Get loading progress (0.0 to 1.0)
     */
    public float getLoadingProgress() {
        if (isFullyLoaded) return 1.0f;
        
        int totalImages = 0;
        int loadedImages = 0;
        
        // Count all required images
        if (standing != null) {
            for (int i = 0; i < standing.length; i++) {
                if (standing[i] != null && standing[i].length >= 2) {
                    totalImages += standing[i][1] - standing[i][0] + 1;
                }
            }
        }
        if (walking != null) {
            for (int i = 0; i < walking.length; i++) {
                if (walking[i] != null && walking[i].length >= 2) {
                    totalImages += walking[i][1] - walking[i][0] + 1;
                }
            }
        }
        if (hit != null) {
            for (int i = 0; i < hit.length; i++) {
                if (hit[i] != null && hit[i].length >= 2) {
                    totalImages += hit[i][1] - hit[i][0] + 1;
                }
            }
        }
        if (attack != null) {
            for (int i = 0; i < attack.length; i++) {
                if (attack[i] != null && attack[i].length >= 2) {
                    totalImages += attack[i][1] - attack[i][0] + 1;
                }
            }
        }
        if (death != null) {
            for (int i = 0; i < death.length; i++) {
                if (death[i] != null && death[i].length >= 2) {
                    totalImages += death[i][1] - death[i][0] + 1;
                }
            }
        }
        
        // Count loaded images in cache
        loadedImages = imageCache.size();
        
        return totalImages > 0 ? (float)loadedImages / totalImages : 0.0f;
    }
    
    /**
     * Static method to start background loading for all bosses
     */
    public static void startBackgroundLoadingForAllBosses() {
        if (isBackgroundLoading) return;
        
        isBackgroundLoading = true;
        backgroundLoadingThread = new Thread(new Runnable() {
            public void run() {
                try {
                    for (BossNew boss : hashBoss.values()) {
                        if (!boss.isFullyLoaded && !boss.isLoading) {
                            boss.loadEssentialImages();
                            try {
                                Thread.sleep(100); // Small delay to prevent overwhelming
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                        }
                    }
                    model.CRes.out("Background loading started for all bosses");
                } finally {
                    isBackgroundLoading = false;
                }
            }
        });
        backgroundLoadingThread.setDaemon(true);
        backgroundLoadingThread.start();
    }
    
    /**
     * Static method to shutdown background loader
     */
    public static void shutdownBackgroundLoader() {
        isBackgroundLoading = false;
        if (backgroundLoadingThread != null && backgroundLoadingThread.isAlive()) {
            backgroundLoadingThread.interrupt();
        }
    }
    
    /**
     * Check if boss data is available and valid
     */
    public boolean isDataValid() {
        return name != null && 
               standing != null && standing.length > 0 &&
               walking != null && walking.length > 0 &&
               hit != null && hit.length > 0 &&
               attack != null && attack.length > 0 &&
               death != null && death.length > 0;
    }
    
    /**
     * Get boss by ID with validation and auto-loading
     */
    public static BossNew getBossWithAutoLoad(byte bossId) {
        BossNew boss = hashBoss.get(bossId);
        
        if (boss == null) {
            model.CRes.out("Boss not found with ID: " + bossId);
            return null;
        }
        
        if (!boss.isDataValid()) {
            model.CRes.out("Boss data invalid for ID: " + bossId);
            return boss; // Return anyway, data might come later
        }
        
        // Auto-trigger loading if not started yet
        if (!boss.isLoading && !boss.isFullyLoaded) {
            boss.loadEssentialImages();
        }
        
        return boss;
    }
    
    /**
     * Force load images for a specific boss (used when entering game)
     */
    public static void ensureBossImagesLoaded(byte bossId) {
        BossNew boss = hashBoss.get(bossId);
        if (boss != null && boss.isDataValid()) {
            if (!boss.isLoading && !boss.isFullyLoaded) {
                boss.loadEssentialImages();
                model.CRes.out("Force loading images for boss: " + boss.name);
            }
        }
    }
    
    /**
     * Load images for all available bosses (called when entering game)
     */
    public static void ensureAllBossImagesLoaded() {
        if (hashBoss.isEmpty()) {
            model.CRes.out("No boss data available for loading");
            return;
        }
        
        // Convert HashMap to array for safe iteration
        BossNew[] bosses = new BossNew[hashBoss.size()];
        int index = 0;
        for (BossNew boss : hashBoss.values()) {
            bosses[index++] = boss;
        }
        
        for (int i = 0; i < bosses.length; i++) {
            BossNew boss = bosses[i];
            if (boss.isDataValid() && !boss.isLoading && !boss.isFullyLoaded) {
                boss.loadEssentialImages();
            }
        }
        
        model.CRes.out("Ensured loading for " + bosses.length + " bosses");
    }
    
    /**
     * Check if any boss data is available
     */
    public static boolean hasBossData() {
        return !hashBoss.isEmpty();
    }
    
    /**
     * Get loading status of all bosses
     */
    public static String getBossLoadingStatus() {
        if (hashBoss.isEmpty()) {
            return "No boss data";
        }
        
        int total = hashBoss.size();
        int loading = 0;
        int loaded = 0;
        
        for (BossNew boss : hashBoss.values()) {
            if (boss.isLoading) {
                loading++;
            } else if (boss.isFullyLoaded) {
                loaded++;
            }
        }
        
        return "Bosses: " + total + ", Loading: " + loading + ", Loaded: " + loaded;
    }
    
    /**
     * Request boss data from server if not available (called when entering game)
     */
    public static void requestBossDataIfNeeded() {
        if (hashBoss.isEmpty()) {
            model.CRes.out("No boss data available, requesting from server...");
            // You can add server request logic here if needed
            // GameService.gI().requestBossData();
        } else {
            model.CRes.out("Boss data already available: " + hashBoss.size() + " bosses");
        }
    }
    
    /**
     * Handle late boss data arrival (when boss data comes after entering game)
     */
    public static void handleLateBossData() {
        if (!hashBoss.isEmpty()) {
            // Convert HashMap to array for safe iteration
            BossNew[] bosses = new BossNew[hashBoss.size()];
            int index = 0;
            for (BossNew boss : hashBoss.values()) {
                bosses[index++] = boss;
            }
            
            for (int i = 0; i < bosses.length; i++) {
                BossNew boss = bosses[i];
                if (boss.isDataValid() && !boss.isLoading && !boss.isFullyLoaded) {
                    boss.loadEssentialImages();
                    model.CRes.out("Late loading boss images: " + boss.name);
                }
            }
        }
    }
}
