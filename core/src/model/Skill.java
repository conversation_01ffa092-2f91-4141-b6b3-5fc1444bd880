package model;

import CLib.RMS;
import CLib.mImage;
import network.GameService;

/**
 * Created by AI Assistant on 9/16/2025.
 * 
 * Skill class for pet skills system
 */
public class Skill {

    public static Skill[] skills;
    public int id;
    public String name;
    public String detail;
    public int iconId;
    public mImage icon;


    public Skill(int id, String name, String detail, int iconId) {
        this.id = id;
        this.detail = detail;
        this.iconId = iconId;
        this.name = name;
    }

    public Skill() {
        //TODO Auto-generated constructor stub
    }

    public void addIcon(int idMIcon, byte[] dataMIcon, short lenMIcon) {
        this.icon = mImage.createImage(dataMIcon, 0, lenMIcon);
        try {
            RMS.saveRMS(this.id + "_" + idMIcon, dataMIcon);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void loadImage() {
        byte[] dataMIcon = RMS.loadRMS(this.id + "_" + this.iconId);
        if (dataMIcon != null) {
            this.icon = mImage.createImage(dataMIcon, 0, dataMIcon.length);
        } else {
            GameService.gI().getMaterialIcon((byte) 6, this.iconId, this.id);
        }
    }
}
