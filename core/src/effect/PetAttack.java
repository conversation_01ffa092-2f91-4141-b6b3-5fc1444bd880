package effect;

import CLib.RMS;
import CLib.mGraphics;
import CLib.mImage;
import coreLG.CCanvas;
import network.GameService;
import screen.GameScr;

public class PetAttack {
    public String petName;
    public int x;
    public int y;
    public int tick;
    public int dx;
    public int dy;
    public int start;
    public int end;
    public int frame;

    public boolean isRemove;

    public void paint(mGraphics g) {
        mImage img = CCanvas.ImagePetMaps.get(petName + "_" + frame);
        if (img != null) {
            g.drawImage(img, x + dx, y + dy, 3, false);
        } else {
            byte[] dataMIcon = RMS.loadRMS(petName + "_" + frame);
            if (dataMIcon != null) {
                img = mImage.createImage(dataMIcon, 0, dataMIcon.length);
                CCanvas.ImagePetMaps.put(petName + "_" + frame, img);
            } else {
                GameService.gI().getPetImage(petName, frame);
            }
        }
    }

    public void update() {
        if (isRemove) {
            GameScr.petAttacks.removeElement(this);
            return;
        }
        if (CCanvas.gameTick % tick == 0) {
            frame++;
            if (frame < start) {
                frame = start;
            }
            if (frame > end) {
                isRemove = true;
                return;
            }
        }
    }
}
