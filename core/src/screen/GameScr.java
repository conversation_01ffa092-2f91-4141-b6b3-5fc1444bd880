package screen;

import CLib.Image;
import CLib.mGraphics;
import CLib.mImage;
import CLib.mSound;
import CLib.mSystem;
import Equipment.Equip;
import com.teamobi.mobiarmy2.GameMidlet;
import com.teamobi.mobiarmy2.MainGame;
import coreLG.CCanvas;
import coreLG.CONFIG;
import coreLG.TerrainMidlet;
import effect.Camera;
import effect.Effect;
import effect.Explosion;
import effect.GiftEffect;
import effect.PetAttack;
import effect.SmokeManager;
import effect.Snow;
import item.BM;
import item.Bullet;
import item.Item;

import java.util.Vector;

import map.Background;
import map.MM;
import model.CRes;
import model.CTime;
import model.ChatPopup;
import model.FilePack;
import model.Font;
import model.FrameImage;
import model.IAction;
import model.IAction2;
import model.Language;
import model.TField;
import model.TimeBomb;
import network.Command;
import network.GameService;
import network.Session_ME;
import player.Boss;
import player.CPlayer;
import player.PM;

public class GameScr extends CScreen {
    public static boolean isBossNew;
    static int WIDTH = 1000;
    public static int HEIGHT = 1000;
    public static final byte GRAPHIC_HIGH = 0;
    public static final byte GRAPHIC_MEDIUM = 1;
    public static final byte GRAPHIC_LOW = 2;
    public static byte curGRAPHIC_LEVEL = 0;
    public static boolean whiteEffect;
    public static boolean electricEffect;
    public static boolean freezeEffect;
    public static boolean suicideEffect;
    public static boolean poisonEffect;
    public boolean nukeEffect;
    public int tN;
    public int tW = 0;
    public int wE;
    public int xE;
    public int tE = 0;
    public static int xNuke;
    public static int yNuke;
    public static int yElectric;
    public static int xElectric;
    public static int xFreeze;
    public static int yFreeze;
    public static int xSuicide;
    public static int ySuicide;
    public static int xPoison;
    public static int yPoison;
    public static mImage airFighter;
    public static mImage imgMode;
    public static mImage lock;
    public static mImage lockImg;
    public static mImage crosshair;
    public static mImage imgInfoPopup;
    public static mImage s_imgITEM;
    public static mImage imgTeam;
    public static mImage imgPlane;
    public static mImage logoGame;
    public static mImage logoII;
    public static mImage imgQuanHam;
    public static mImage imgBack;
    public static mImage imgMap;
    public static mImage imgUIBoss;
    public static mImage trangbiTileImg;
    public static mImage shopTileImg;
    public static mImage tienBarImg;
    public static mImage soLuongBarImg;
    public static mImage buyBar;
    public static mImage ladySexyImg;
    public static mImage imgCurPos;
    public static mImage imgSmallCloud;
    public static mImage imgArrowRed;
    public static mImage imgRoomStat;
    public static mImage imgTrs;
    public static mImage imgIcon;
    public static mImage itemBarImg;
    public static mImage imgChat;
    public static mImage s_imgTransparent;
    public static mImage arrowMenu;
    public static mImage wind1;
    public static mImage wind2;
    public static mImage wind3;
    public static mImage trai;
    public static mImage phai;
    public static mImage crossHair2;
    public static mImage nut_up;
    public static mImage nut_down;
    public static mImage bom_black;
    public static mImage bom_gold;
    public static mImage gathuyentruong;
    public static mImage nuvuong;
    public static mImage quocvuongga;
    public static mImage tathan;
    public static mImage button;
    public static mImage buttonMenu;

    public static mImage[] imgReady = new mImage[9];
    public static mImage[] imgMsg = new mImage[2];
    public static Vector<TimeBomb> timeBombs = new Vector();
    public static TField tfChat;
    public static MM mm;
    public static PM pm;
    public static Camera cam;
    public static BM bm;
    public static Vector<Explosion> exs;
    public static Vector<PetAttack> petAttacks;
    public static SmokeManager sm;
    public static CTime time;
    public Vector vGift = new Vector();
    public static int windx;
    public static int windy;
    int teamSize;
    int mapID;
    public static boolean trainingMode;
    mImage pause;
    public static int tickCount;
    public static byte ID_Turn;
    public static mImage s_imgAngle;
    public static FrameImage s_frBar;
    public static FrameImage s_frWind;
    public static boolean isDarkEffect;
    public static int s_iPlane_x;
    public static int s_iPlane_y;
    public static int s_iBombTargetX;
    public static byte room;
    public static byte board;
    byte exBonus;
    int moneyBonus;
    int moneyY = -100;
    public static String res;
    int moneyBonus2;
    int moneyY2;
    boolean isMoney2Fly;
    int whoGetMoney2;
    boolean isMoneyFly;
    int nBoLuot;
    private boolean isSelectItem;
    public static int curItemSelec;
    private long timeDelayClosePauseMenu;
    public static byte myIndex;
    Vector chatList = new Vector();
    int chatDelay;
    int MAX_CHAT_DELAY = 40;
    Snow snow;
    public static boolean iconOnOf;
    public boolean isShowPausemenu;
    public long timeShowPauseMenu;
    int chatWait = 0;
    boolean isChat;
    public boolean isFly;
    public String text = "";
    public int xFly;
    public int yFly;
    public int tFly;
    public Equip equip;
    
    // Player menu variables
    public boolean isShowPlayerMenu;
    public CPlayer selectedPlayer;
    public int playerMenuX;
    public int playerMenuY;
    public static int trainingStep;
    public static boolean isUpdateHP;
    int left = 0;
    int right = 1;
    int up = 2;
    int down = 3;
    public static boolean cantSee;
    public byte whoCantSee;
    public static int xL;
    public static int yL;
    public static int xR;
    public static int yR;
    public static int xF;
    public static int yF;
    public static int xU;
    public static int yU;
    public static int xD;
    public static int yD;
    static mImage imgArrow;
    public static int windAngle;
    public static int windPower;
    public int t1;
    public int t2;
    public int dem;
    boolean b;
    public static byte[] num;
    boolean isPressXL;
    boolean isPressXR;
    boolean isPressXF;

    static {
        FilePack filePak = null;

        try {
            filePak = new FilePack(CCanvas.getClassPathConfig(CONFIG.PATH_GUI + "gui"));
            // airFighter = filePak.loadImage("fighter.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "fighter");
            //     }
            // });
            GameScr.airFighter = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/fighter.png");

            // imgMode = filePak.loadImage("mode.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "mode");
            //     }
            // });
            GameScr.imgMode = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/mode.png");

            // lock = filePak.loadImage("lock2.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "lock2");
            //     }
            // });
            GameScr.lock = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/lock2.png");

            // lockImg = filePak.loadImage("lock.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "lock");
            //     }
            // });
            GameScr.lockImg = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/lock.png");

            // crosshair = filePak.loadImage("hongTam.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "hongTam");
            //     }
            // });
            GameScr.crosshair = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/hongTam.png");

            // imgInfoPopup = filePak.loadImage("popupRound.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "popupRound");
            //     }
            // });
            GameScr.imgInfoPopup = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/popupRound.png");
            // s_imgITEM = filePak.loadImage("item.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "item");
            //     }
            // });
            GameScr.s_imgITEM = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/item.png");
            // imgPlane = filePak.loadImage("fighter.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "fighter");
            //     }
            // });
            GameScr.imgPlane = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/fighter.png");

            logoGame = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/logoGame.png");
            // imgReady[0] = filePak.loadImage("on.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "on");
            //     }
            // });
            GameScr.imgReady[0] = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/on.png");

            // imgReady[1] = filePak.loadImage("off.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "off");
            //     }
            // });
            GameScr.imgReady[1] = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/off.png");

            // imgReady[2] = filePak.loadImage("r2.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "r2");
            //     }
            // });
            GameScr.imgReady[2] = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/r2.png");


            // imgReady[3] = filePak.loadImage("arrowup.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "arrowup");
            //     }
            // });
            GameScr.imgReady[3] = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/arrowup.png");

            imgReady[4] = filePak.loadImage("tile1.png", new IAction2() {
                public void perform(Object object) {
                    CRes.onSaveToFile((Image) object, "tile1");
                }
            });
            // imgQuanHam = filePak.loadImage("quanham.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "quanham");
            //     }
            // });
            GameScr.imgQuanHam = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/quanham.png");

            // imgBack = filePak.loadImage("menubg.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "menubg");
            //     }
            // });
            GameScr.imgBack = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/menubg.png");

            // imgCurPos = filePak.loadImage("curMapPos.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "curMapPos");
            //     }
            // });
            GameScr.imgCurPos = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/curMapPos.png");

            imgSmallCloud = PrepareScr.cloud1;
            // imgArrowRed = filePak.loadImage("arrowRed.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "arrowRed");
            //     }
            // });
            GameScr.imgArrowRed = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/arrowRed.png");

            // imgRoomStat = filePak.loadImage("stat.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "stat");
            //     }
            // });
            GameScr.imgRoomStat = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/stat.png");

            // imgTrs = filePak.loadImage("trs.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "trs");
            //     }
            // });
            GameScr.imgTrs = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/trs.png");

            // imgIcon = filePak.loadImage("icon.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "icon");
            //     }
            // });
            GameScr.imgIcon = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/icon.png");

            // s_imgAngle = filePak.loadImage("angle.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "angle");
            //     }
            // });
            GameScr.s_imgAngle = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/angle.png");

            // imgChat = filePak.loadImage("chat.png", new IAction2() {
            //     public void perform(Object object) {
            //     }
            // });
            GameScr.imgChat = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/chat.png");

            // s_imgTransparent = filePak.loadImage("transparent.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "transparent");
            //     }
            // });
            GameScr.s_imgTransparent = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/transparent.png");


            // imgMsg[0] = filePak.loadImage("msg0.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "msg0");
            //     }
            // });
            GameScr.imgMsg[0] = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/msg0.png");

            // imgMsg[1] = filePak.loadImage("msg1.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "msg1");
            //     }
            // });
            GameScr.imgMsg[1] = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/msg1.png");

            // logoII = filePak.loadImage("logo_2.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "logo_2");
            //     }
            // });
            GameScr.logoII = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/logo_2.png");

            // arrowMenu = filePak.loadImage("arrowMenu.png", new IAction2() {
            //     public void perform(Object object) {
            //         CRes.onSaveToFile((Image) object, "arrowMenu");
            //     }
            // });
            GameScr.arrowMenu = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/arrowMenu.png");
            GameScr.button = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/xx_button.png");
            GameScr.buttonMenu = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/x_button_menu.png");

            GameScr.imgUIBoss = mImage.createImage("/gui/khung-bosss.png");
            GameScr.trai = mImage.createImage("/gui/nut2.png");
            GameScr.phai = mImage.createImage("/gui/nut1.png");
            GameScr.crossHair2 = mImage.createImage("/gui/nut3.png");
            GameScr.nut_up = mImage.createImage("/gui/nut_up.png");
            GameScr.nut_down = mImage.createImage("/gui/nut_down.png");
            GameScr.wind1 = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/wind.png");
            GameScr.wind2 = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/wind2.png");
            GameScr.wind3 = mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/wind3.png");
            GameScr.bom_black = mImage.createImage("/icon_boss/bom_black.png");
            GameScr.bom_gold = mImage.createImage("/icon_boss/bom_gold.png");
            GameScr.gathuyentruong = mImage.createImage("/icon_boss/gathuyentruong.png");
            GameScr.nuvuong = mImage.createImage("/icon_boss/nuvuong_kien.png");
            GameScr.quocvuongga = mImage.createImage("/icon_boss/quocvuongga.png");
            GameScr.tathan = mImage.createImage("/icon_boss/tathan.png");
            mImage.createImage("/x"+mGraphics.zoomLevel+"/gui/barMove.png", new IAction2() {
                public void perform(Object object) {
                    Image img = (Image) object;
                    GameScr.s_frBar = new FrameImage(img, 53, 12, false);
                }
            });
        } catch (Exception var3) {
            var3.printStackTrace();
        }

        filePak = null;
        tickCount = 0;
        ID_Turn = 0;
        isDarkEffect = false;
        s_iPlane_x = -1;
        s_iPlane_y = -1;
        s_iBombTargetX = -1;
        res = "";

        try {
            imgArrow = mImage.createImage("/arrow.png");
        } catch (Exception var2) {
        }

        num = new byte[8];
    }

    public GameScr() {
        this.initGamescr();
    }

    public void initGamescr() {
        mm = new MM();
        pm = new PM();
        cam = new Camera();
        bm = new BM();
        sm = new SmokeManager();
        time = new CTime();
        tfChat = new TField();
        tfChat.x = 2;
        tfChat.y = CCanvas.hieght - ITEM_HEIGHT - 25;
        if (CCanvas.isTouch) {
            tfChat.y = CCanvas.hieght - CScreen.cmdH - ITEM_HEIGHT;
        }

        tfChat.width = CCanvas.width - 4;
        tfChat.height = ITEM_HEIGHT + 2;
        tfChat.setisFocus(true);
        tfChat.nameDebug = "Tfield ====> Gamescr";
        this.nameCScreen = "GameScr screen!";
    }

    public void initGame(byte MapID, byte timeInterval, short[] playerX, short[] playerY, short[] maxHP, int team) {
        isDarkEffect = false;
        s_iPlane_x = -1;
        s_iPlane_y = -1;
        s_iBombTargetX = -1;
        this.isMoneyFly = false;
        res = "";
        this.nBoLuot = 3;
        iconOnOf = true;
        if (curGRAPHIC_LEVEL != 2) {
            mm.createBackGround();
        }

        switch (curGRAPHIC_LEVEL) {
            case 0:
            case 1:
                if (!Background.isLoadImage) {
                    Background.isLoadImage = true;
                    Background.initImage();
                }
                break;
            case 2:
                Background.removeImage();
        }

        this.initGamescr();
        pm.init();
        CPlayer.isStopFire = false;
        bm.onInitSpecialBullet();
        exs = new Vector();
        petAttacks = new Vector();
        timeBombs = new Vector();
        if (team != 0) {
            CCanvas.gameScr.flyText("+" + team + Language.diemdongdoi(), CCanvas.width / 2, CCanvas.hieght - 50, (Equip) null);
        }

        time.initTimeInterval(timeInterval);
        this.snow = null;
        if (curGRAPHIC_LEVEL != 2) {
            if (Background.curBGType == 2) {
                this.snow = new Snow();
                this.snow.startSnow(0);
            }

            if (Background.curBGType == 10) {
                this.snow = new Snow();
                if (MM.mapID == 34) {
                    this.snow.waterY = 35;
                }

                if (MM.mapID == 35) {
                    this.snow.waterY = 30;
                }

                if (MM.mapID == 38) {
                    this.snow.waterY = 80;
                }

                if (MM.mapID == 39) {
                    this.snow.waterY = 0;
                }

                this.snow.startSnow(1);
            }
        }

        pm.initPlayer(playerX, playerY, maxHP);

        // Play background music based on map ID
        mSound.playMapMusic(MM.mapID);

        for (int i = 0; i < PM.p.length; ++i) {
            if (PM.p[i] != null) {
                PM.p[i].cantSee = false;
            }
        }

        cantSee = false;
        Bullet.webId = 200;
    }

    private void onDragCamera(int xDrag, int yDrag, int index) {
        if (Camera.mode == 0) {
            int deltaX = xDrag - CCanvas.pxFirst[index];
            int deltaY = yDrag - CCanvas.pyFirst[index];
            if (deltaX > 1) {
                Camera.dx2 -= Math.abs(deltaX) >> 2;
            } else if (deltaX < -1) {
                Camera.dx2 += Math.abs(deltaX) >> 2;
            }

            if (deltaY > 1) {
                Camera.dy2 -= Math.abs(deltaY) >> 2;
            } else if (deltaY < -1) {
                Camera.dy2 += Math.abs(deltaY) >> 2;
            }
        }

    }

    public void selectedItemPanelRealeased(int x, int y2, int index) {
        int num = PM.getMyPlayer().item.length;
        int itemW = 0;
        int itemH = 0;
        int xItem = CCanvas.hw - 80;
        int yItem = CCanvas.hh - 33;
        if (CCanvas.isPointer(xItem, yItem, 170, 90, index)) {
            int aa = (y2 - yItem) / 40 * 4 + (x - xItem) / 40;
            if (aa >= 0 && aa <= 7) {
                if (aa != curItemSelec) {
                    curItemSelec = aa;
                } else if (CCanvas.isDoubleClick) {
                    int[] itemList = PM.getMyPlayer().item;
                    if (trainingMode) {
                        clearKey();
                        PM.getMyPlayer().UseItem(itemList[curItemSelec], true, curItemSelec);
                        if (itemList[curItemSelec] == 0) {
                            CPlayer var10000 = PM.p[0];
                            var10000.hp += 30;
                        }

                        this.isSelectItem = false;
                        this.timeDelayClosePauseMenu = mSystem.currentTimeMillis() + 300L;
                    } else {
                        CRes.out("itemList: " + itemList.length);

                        for (int i = 0; i < itemList.length; ++i) {
                            CRes.out("itemList: " + i + "_value_" + itemList[i]);
                        }

                        CRes.out("index/idItem/ItemUsed: " + curItemSelec + "/" + itemList[curItemSelec] + "/" + PM.getMyPlayer().itemUsed);
                        if (PM.getMyPlayer().itemUsed != -1 || itemList[curItemSelec] == -2 || itemList[curItemSelec] == -1) {
                            this.isSelectItem = false;
                            return;
                        }

                        if (pm.isYourTurn()) {
                            if (PrepareScr.currLevel == 7) {
                                if (GameScr.num[curItemSelec] != 0) {
                                    PM.getMyPlayer().UseItem(itemList[curItemSelec], false, curItemSelec);
                                    this.isSelectItem = false;
                                    this.timeDelayClosePauseMenu = mSystem.currentTimeMillis() + 300L;
                                }
                            } else {
                                PM.getMyPlayer().UseItem(itemList[curItemSelec], false, curItemSelec);
                                this.isSelectItem = false;
                                this.timeDelayClosePauseMenu = mSystem.currentTimeMillis() + 300L;
                            }
                        }

                        clearKey();
                    }
                }
            }
        }

        if (!CCanvas.isPointer(xItem - 50, yItem - 50, 210, 130, index) && this.isSelectItem) {
            this.isSelectItem = false;
            this.timeDelayClosePauseMenu = mSystem.currentTimeMillis() + 550L;
        }

    }

    public void flyText(String text, int xFly, int yFly, Equip equip) {
        this.isFly = true;
        this.text = text;
        this.xFly = xFly;
        this.yFly = yFly;
        this.equip = equip;
    }

    protected void doSetForce() {
        CCanvas.inputDlg.setInfo("Lực max 1-30", new IAction() {
            public void perform() {
                try {
                    PM.getMyPlayer().maxforce = Integer.parseInt(CCanvas.inputDlg.tfInput.getText());
                    if (PM.getMyPlayer().maxforce < 1) {
                        PM.getMyPlayer().maxforce = 1;
                    }
                    if (PM.getMyPlayer().maxforce > 30) {
                        PM.getMyPlayer().maxforce = 30;
                    }
                    CCanvas.endDlg();
                    if (PM.getMyPlayer().gun == 6 || PM.getMyPlayer().gun == 8) {
                        CCanvas.inputDlg.setInfo("Lực max 2 1-30", new IAction() {
                            public void perform() {
                                try {
                                    PM.getMyPlayer().maxforce2 = Integer.parseInt(CCanvas.inputDlg.tfInput.getText());
                                    if (PM.getMyPlayer().maxforce2 < 1) {
                                        PM.getMyPlayer().maxforce2 = 1;
                                    }
                                    if (PM.getMyPlayer().maxforce2 > 30) {
                                        PM.getMyPlayer().maxforce2 = 30;
                                    }
                                } catch (Exception var3) {
                                    PM.getMyPlayer().maxforce2 = 30;
                                }
                                CCanvas.endDlg();
                            }
                        }, new IAction() {
                            public void perform() {
                                CCanvas.endDlg();
                            }
                        }, 1);
                        CCanvas.inputDlg.tfInput.setText("" + PM.getMyPlayer().maxforce2);
                        CCanvas.inputDlg.show();
                    }
                } catch (Exception var3) {
                    PM.getMyPlayer().maxforce = 30;
                }

            }
        }, new IAction() {
            public void perform() {
                CCanvas.endDlg();
            }
        }, 1);
        CCanvas.inputDlg.tfInput.setText("" + PM.getMyPlayer().maxforce);
        CCanvas.inputDlg.show();
    }

    public void doShowPauseMenu() {
        this.isShowPausemenu = true;
        Vector<Command> menu = new Vector();
        menu.addElement(new Command(Language.CONTINUE(), new IAction() {
            public void perform() {
                GameScr.this.isShowPausemenu = false;
                GameScr.this.timeShowPauseMenu = mSystem.currentTimeMillis() + 300L;
            }
        }));

        menu.addElement(new Command("LỰC MAX", new IAction() {
            public void perform() {
                doSetForce();
            }
        }));

        if (pm.isYourTurn()) {
            menu.addElement(new Command(Language.USEITEM(), new IAction() {
                public void perform() {
                    if (GameScr.pm.isYourTurn()) {
                        GameScr.this.isSelectItem = true;
                    }

                    GameScr.this.isShowPausemenu = false;
                    GameScr.curItemSelec = 7;
                    GameScr.this.timeShowPauseMenu = mSystem.currentTimeMillis() + 300L;
                }
            }));
        }

        if (pm.isYourTurn() && PM.getCurPlayer().isAngry && !PM.getCurPlayer().isUsedItem) {
            menu.addElement(new Command(Language.SPECIAL(), new IAction() {
                public void perform() {
                    GameService.gI().useItem((byte) 100);
                    PM.getCurPlayer().isUsedItem = true;
                    PM.getCurPlayer().itemUsed = 100;
                    PM.getCurPlayer().angryX = 0;
                    PM.getCurPlayer().currAngry = 0;
                    PM.getCurPlayer().is2TurnItem = true;
                    GameScr.this.isShowPausemenu = false;
                    GameScr.this.timeShowPauseMenu = mSystem.currentTimeMillis() + 300L;
                }
            }));
        }

        menu.addElement(new Command(Language.battaticon(), new IAction() {
            public void perform() {
                GameScr.iconOnOf = !GameScr.iconOnOf;
                GameScr.this.isShowPausemenu = false;
                GameScr.this.timeShowPauseMenu = mSystem.currentTimeMillis() + 300L;
            }
        }));
        if (pm.isYourTurn() && !trainingMode && !BM.active && PM.getMyPlayer().active && this.nBoLuot > 0) {
            menu.addElement(new Command(Language.SKIP(), new IAction() {
                public void perform() {
                    GameScr.time.skipTurn();
                    --GameScr.this.nBoLuot;
                    GameScr.this.isShowPausemenu = false;
                    GameScr.this.timeShowPauseMenu = mSystem.currentTimeMillis() + 300L;
                }
            }));
        }

        menu.addElement(new Command(Language.LEAVEBATTLE(), new IAction() {
            public void perform() {
                if (GameScr.trainingMode) {
                    GameService.gI().training((byte) 1);
                    GameScr.trainingMode = false;
                    GameScr.this.isShowPausemenu = false;
                    GameScr.this.timeShowPauseMenu = mSystem.currentTimeMillis() + 300L;
                    CScreen.isSetClip = true;
                } else if (PM.p[GameScr.myIndex].getState() == 5) {
                    CCanvas.startYesNoDlg(Language.youWillLose(), new IAction() {
                        public void perform() {
                            GameScr.this.exitGiuaChung();
                            CCanvas.endDlg();
                            GameScr.this.isShowPausemenu = false;
                            GameScr.this.timeShowPauseMenu = mSystem.currentTimeMillis() + 300L;
                        }
                    });
                } else {
                    CCanvas.startYesNoDlg(Language.wantExit(), new IAction() {
                        public void perform() {
                            GameScr.this.exitGiuaChung();
                            CCanvas.endDlg();
                            GameScr.this.isShowPausemenu = false;
                            GameScr.this.timeShowPauseMenu = mSystem.currentTimeMillis() + 300L;
                        }
                    });
                }
            }
        }));
        CCanvas.pausemenu.startAt(menu);
    }

    public void addTimeBomb(TimeBomb bomb) {
        timeBombs.addElement(bomb);
        waitting();
    }

    public void explodeTimeBomb(int id) {
        for (int i = 0; i < timeBombs.size(); ++i) {
            TimeBomb b = (TimeBomb) timeBombs.elementAt(i);
            if (b.id == id) {
                b.isExplore = true;
                mm.makeHole(b.x, b.y, (byte) 57, 9);
                return;
            }
        }

        waitting();
    }

    public static void waitting() {
        CTime.seconds += 2;
        CCanvas.tNotify = 0;
        CCanvas.lockNotify = true;
        if (CCanvas.curScr == CCanvas.gameScr) {
            Session_ME.receiveSynchronized = 1;
        }

    }

    public void exitGiuaChung() {
        if (pm != null && PM.p != null && PM.p[myIndex] != null) {
            // Close pause menu when leaving battle
            this.isShowPausemenu = false;
            if (CCanvas.pausemenu != null) {
                CCanvas.pausemenu.isShow = false;
            }
            
            // Stop map music when leaving battle
            mSound.stopMapMusic();
            
            GameService.gI().leaveBoard();
            CCanvas.startWaitDlgWithoutCancel(Language.leaveBattle(), 9);
            GameService.gI().requestRoomList();
            CScreen.isSetClip = true;
        }

    }

    public void doExit() {
        for (int i = 0; i < PM.MAX_PLAYER; ++i) {
            if (PM.p[i] != null) {
                PM.p[i] = null;
            }
        }

        // Close pause menu when exiting to ensure clean state
        this.isShowPausemenu = false;
        if (CCanvas.pausemenu != null) {
            CCanvas.pausemenu.isShow = false;
        }

        // Stop map music when exiting game
        mSound.stopMapMusic();

        CCanvas.prepareScr.show();
        Session_ME.receiveSynchronized = 0;
    }

    public void update() {
        if (trainingMode) {
            this.doTraining();
        }

        if (this.chatWait > 0) {
            --this.chatWait;
        }

        tfChat.update();
        this.updateChat();
        bm.update();
        pm.update();
        sm.update();
        cam.update();
        if (this.snow != null) {
            this.snow.update();
        }

        int i;
        for (i = 0; i < timeBombs.size(); ++i) {
            TimeBomb b = (TimeBomb) timeBombs.elementAt(i);
            if (b != null) {
                b.update();
            }
        }

        for (i = 0; i < exs.size(); ++i) {
            ((Explosion) exs.elementAt(i)).update();
        }
        for (i = 0; i < petAttacks.size(); ++i) {
            ((PetAttack) petAttacks.elementAt(i)).update();
        }

        time.update();
        ++tickCount;
        if (tickCount > 10000) {
            tickCount = 0;
        }

        if (this.isMoneyFly) {
            --this.moneyY;
            if (this.moneyY < 50) {
                this.isMoneyFly = false;
                this.moneyY = h / 2 - 15;
            }
        }

        if (this.isMoney2Fly) {
            --this.moneyY2;
            if (this.moneyY2 < PM.p[this.whoGetMoney2].y + 100) {
                this.isMoney2Fly = false;
            }
        }

        if (this.vGift.size() != 0) {
            ++this.tFly;
            if (this.tFly == 10) {
                for (i = 0; i < this.vGift.size(); ++i) {
                    if (!((GiftEffect) this.vGift.elementAt(i)).isFly) {
                        ((GiftEffect) this.vGift.elementAt(i)).isFly = true;
                        break;
                    }
                }

                this.tFly = 0;
            }
        }

        for (i = 0; i < this.vGift.size(); ++i) {
            ((GiftEffect) this.vGift.elementAt(i)).update();
        }

    }

    public void mainLoop() {
        super.mainLoop();
        mm.update();
        cam.mainLoop();
    }

    private void doTraining() {
        switch (trainingStep) {
            case 0:
                if (!PM.p[0].falling) {
                    trainingStep = -1;
                    CCanvas.startOKDlg(Language.training1(), new IAction() {
                        public void perform() {
                            CCanvas.startOKDlg(Language.training2(), new IAction() {
                                public void perform() {
                                    GameScr.trainingStep = 1;
                                }
                            });
                        }
                    });
                }
                break;
            case 1:
                if (PM.p[0].movePoint > 20) {
                    trainingStep = -1;
                    CCanvas.startOKDlg(Language.trainin3(), new IAction() {
                        public void perform() {
                            CCanvas.startOKDlg(Language.training4(), new IAction() {
                                public void perform() {
                                    CCanvas.startOKDlg(Language.training5(), new IAction() {
                                        public void perform() {
                                            GameScr.trainingStep = 2;
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
                break;
            case 2:
                if (PM.p[1].y > 514) {
                    trainingStep = -1;
                    CCanvas.startOKDlg(Language.training6(), new IAction() {
                        public void perform() {
                            GameScr.trainingStep = 3;
                        }
                    });
                }
                break;
            case 3:
                if (PM.getMyPlayer().hp == 100 || PM.p[1].y > MM.mapHeight || PM.getMyPlayer().y > MM.mapHeight) {
                    trainingStep = -1;
                    CCanvas.startOKDlg(Language.training7(), new IAction() {
                        public void perform() {
                            GameScr.trainingStep = 0;
                            GameScr.trainingMode = false;
                            GameService.gI().training((byte) 1);
                            CCanvas.menuScr.show();
                        }
                    });
                }
        }

    }

    public void activeMoney2Fly(int money, int ID_of_whoget) {
        if (PM.p[PM.getIndexByIDDB(ID_of_whoget)] != null && PM.p != null) {
            if (money > 0) {
                this.showChat(ID_of_whoget, " +" + money + Language.xu());
            } else {
                this.showChat(ID_of_whoget, " " + money + Language.xu());
            }

        }
    }

    public void setWin(byte isWin, byte _exBonus, int _moneyBonus) {
        this.chatList.removeAllElements();
        this.exBonus = _exBonus;
        this.moneyBonus = _moneyBonus;
        this.moneyY = CScreen.h / 2;
        this.isMoneyFly = true;
        time.stop();
        
        // Close pause menu when game ends to prevent UI getting stuck
        this.isShowPausemenu = false;
        if (CCanvas.pausemenu != null) {
            CCanvas.pausemenu.isShow = false;
        }
        if (isWin == 0) {
            res = Language.RAW();
            pm.setPlayerAfterDraw();
        } else if (PM.p[myIndex] != null) {
            boolean teamWin = false;
            if (isWin == 1) {
                res = Language.WIN();
                if (PM.p[myIndex].team) {
                    teamWin = true;
                } else {
                    teamWin = false;
                }
            } else {
                res = Language.LOSE();
                if (PM.p[myIndex].team) {
                    teamWin = false;
                } else {
                    teamWin = true;
                }
            }

            if (teamWin == PM.p[myIndex].team) {
                pm.setPlayerAfterSetWin(teamWin);
            }
        }

    }

    public static int[][] getPointAround(int x, int y, int number) {
        int[] xPoint = new int[number];
        int[] yPoint = new int[number];
        xPoint[6] = x - 10;
        yPoint[6] = y;
        xPoint[5] = x + 10;
        yPoint[5] = y;
        xPoint[4] = x;
        yPoint[4] = y - 35;
        xPoint[3] = x;
        yPoint[3] = y - 70;
        xPoint[2] = x - 30;
        yPoint[2] = y - 70;
        xPoint[1] = x + 30;
        yPoint[1] = y - 70;
        xPoint[0] = x;
        yPoint[0] = y - 85;
        return new int[][]{xPoint, yPoint};
    }

    public void checkEyeSmoke(byte whoCantSee, byte typeSee) {
        if (typeSee == 1) {
            PM.p[whoCantSee].cantSee = false;
            if (whoCantSee == myIndex) {
                cantSee = false;
            }
        } else {
            PM.p[whoCantSee].cantSee = true;
            if (whoCantSee == myIndex) {
                cantSee = true;
            }
        }

        waitting();
    }

    public void checkInvisible2(byte whoInvisible) {
        PM.p[whoInvisible].isInvisible = false;
        cam.setPlayerMode(whoInvisible);
        waitting();
    }

    public void checkVampire(byte whoVampire) {
        PM.p[whoVampire].isVampire = false;
        cam.setPlayerMode(whoVampire);
        waitting();
    }

    public void checkFreeze(byte whoCantMove, byte type) {
        if (type == 1) {
            PM.p[whoCantMove].isFreeze = false;
        } else {
            PM.p[whoCantMove].isFreeze = true;
        }

        waitting();
    }

    public void checkPostion(byte whoPoison) {
        PM.p[whoPoison].isPoison = true;
        waitting();
    }

    private void paintTouch(mGraphics g) {
        int xCenter = 0;
        int yCenter = CCanvas.hieght - phai.image.height - 10;
        xL = xCenter + 35;
        yL = yCenter;
        xR = xCenter + 140;
        yR = yCenter;
        xF = CCanvas.width - (crossHair2.image.width + crossHair2.image.width / 2);
        yF = CCanvas.hieght - (crossHair2.image.height + crossHair2.image.height / 2);
        byte xOffset;
        byte yOffset;
        if (CCanvas.isDebugging()) {
            g.setColor(16765440);
            xOffset = 30;
            yOffset = 30;
            g.fillRect(xF - xOffset / 2, yF - yOffset / 2, crossHair2.image.getWidth() + xOffset, crossHair2.image.getHeight() + yOffset, false);
        }

        if (this.isPressXF) {
            g.drawImage(crossHair2, xF + 2, yF + 2, mGraphics.TOP | mGraphics.LEFT, false);
        } else {
            g.drawImage(crossHair2, xF, yF, mGraphics.TOP | mGraphics.LEFT, false);
        }

        if (CCanvas.isDebugging()) {
            g.setColor(16765440);
            xOffset = 30;
            yOffset = 30;
            g.fillRect(xL - trai.image.width / 2 - xOffset / 2, yL - trai.image.height / 2 - yOffset / 2, trai.image.getWidth() + xOffset, trai.image.getHeight() + yOffset, false);
        }

        if (this.isPressXL) {
            g.drawImage(trai, xL + 2, yL + 2, mGraphics.VCENTER | mGraphics.HCENTER, false);
        } else {
            g.drawImage(trai, xL, yL, mGraphics.VCENTER | mGraphics.HCENTER, false);
        }

        if (CCanvas.isDebugging()) {
            g.setColor(16765440);
            xOffset = 30;
            yOffset = 30;
            g.fillRect(xR - phai.image.width / 2 - xOffset / 2, yR - trai.image.height / 2 - yOffset / 2, phai.image.getWidth() + xOffset, phai.image.getHeight() + yOffset, false);
        }

        if (this.isPressXR) {
            g.drawImage(phai, xR + 2, yR + 2, mGraphics.VCENTER | mGraphics.HCENTER, false);
        } else {
            g.drawImage(phai, xR, yR, mGraphics.VCENTER | mGraphics.HCENTER, false);
        }

    }

    public void paint(mGraphics g) {
        Camera.translate(g);
        if (curGRAPHIC_LEVEL != 2) {
            mm.paintBackGround(g);
        } else {
            g.setColor(6483442);
            g.fillRect(Camera.x, Camera.y, CCanvas.width, CCanvas.hieght, false);
        }

        if (this.snow != null) {
            this.snow.paintSmallSnow(g);
        }

        mm.paint(g);

        int i;
        for (i = 0; i < this.vGift.size(); ++i) {
            ((GiftEffect) this.vGift.elementAt(i)).paint(g);
        }

        if (isDarkEffect) {
            Effect.FillTransparentRect(g, Camera.x, Camera.y, w, h);
        }

        if (cantSee) {
            g.setColor(16777215);
            g.fillRect(Camera.x, Camera.y, w, h, false);
        }

        for (i = 0; i < timeBombs.size(); ++i) {
            TimeBomb bomb = (TimeBomb) timeBombs.elementAt(i);
            if (bomb != null) {
                bomb.paint(g);
            }
        }

        pm.paint(g);

        for (i = 0; i < PM.p.length; ++i) {
            if (PM.p[i] != null && PM.p[i].isFreeze) {
                g.drawImage(Explosion.dongbang, PM.p[i].x, PM.p[i].y - 12, 3, false);
            }
        }

        sm.paint(g);
        bm.paint(g);
        if (MM.isHaveWaterOrGlass) {
            mm.paintWater(g);
        }

        for (i = 0; i < exs.size(); ++i) {
            ((Explosion) exs.elementAt(i)).paint(g);
        }
        for (i = 0; i < petAttacks.size(); ++i) {
            ((PetAttack) petAttacks.elementAt(i)).paint(g);
        }

        if (this.snow != null) {
            this.snow.paintBigSnow(g);
        }

        int xPaint;
        if (whiteEffect) {
            g.setColor(16777215);
            g.fillArc(Camera.x + CCanvas.width / 2 - this.xE, Camera.y + CCanvas.width / 2 - this.xE, this.wE, this.wE, 0, 360, false);
            this.xE += 30;
            this.wE += 60;
            if (this.xE > CCanvas.width + 100) {
                this.xE = 0;
                this.wE = 0;
                int[][] array = getPointAround(xNuke, yNuke, 7);

                for (xPaint = 0; xPaint < 7; ++xPaint) {
                    new Explosion(array[0][xPaint], array[1][xPaint], (byte) 7);
                }

                whiteEffect = false;
            }
        }

        if (electricEffect) {
            ++this.tE;
            if (this.tE % 2 == 0) {
                new Explosion(xElectric + CRes.random(-20, 20), yElectric + CRes.random(-20, 20), (byte) 8);
            }

            if (this.tE == 10) {
                this.tE = 0;
                electricEffect = false;
            }
        }

        if (freezeEffect) {
            ++this.tE;
            if (this.tE % 2 == 0) {
                new Explosion(xFreeze + CRes.random(-50, 50), yFreeze + CRes.random(-50, 50), (byte) 14);
            }

            if (this.tE == 30) {
                this.tE = 0;
                freezeEffect = false;
            }
        }

        if (suicideEffect) {
            ++this.tE;
            if (this.tE % 2 == 0) {
                new Explosion(xSuicide + CRes.random(-50, 50), ySuicide + CRes.random(-50, 50), (byte) 0);
            }

            if (this.tE == 60) {
                this.tE = 0;
                suicideEffect = false;
            }
        }

        if (poisonEffect) {
            ++this.tE;
            if (this.tE % 2 == 0) {
                new Explosion(xPoison + CRes.random(-50, 50), yPoison + CRes.random(-50, 50), (byte) 15);
            }

            if (this.tE == 60) {
                this.tE = 0;
                poisonEffect = false;
            }
        }

        if (CCanvas.isDebugging()) {
            for (i = 0; i < MM.mapWidth / 100; ++i) {
                g.setColor(16711680);
                g.drawLine(100 * (i + 1), 0, 100 * (i + 1), MM.mapHeight, false);
                Font.normalFont.drawString(g, String.valueOf(i), 50 + i * 100, CCanvas.h / 2, 0);
            }
        }

        if (Camera.shaking == 2 && tickCount / 2 % 2 == 0) {
            g.setColor(16711680);
            g.fillRect(Camera.x, Camera.y, w, 10, false);
            g.fillRect(Camera.x, Camera.y + h - 10, w, 10, false);
            g.fillRect(Camera.x, Camera.y, 10, h, false);
            g.fillRect(Camera.x + w - 10, Camera.y, 10, h, false);
        }

        if (!trainingMode) {
            time.paint(g);
        }

        int yPaint;
        if (pm.isYourTurn() && PM.getCurPlayer() != null) {
            int forceT1 = PM.getMyPlayer().getState() == 3 ? PM.getMyPlayer().force : 0;
            int forceT2 = PM.getMyPlayer().getState() == 3 ? PM.getMyPlayer().force_2 : 0;
            yPaint = PM.getMyPlayer().movePoint;
            int lastForcePoint1 = PM.getMyPlayer().lastForcePoint;
            int lastForcePoint2 = PM.getMyPlayer().lastForcePoint_2;
            if (!this.isSelectItem && MainGame.getNumberFingerOnScreen() < 2 && Camera.mode != 0 && !CPlayer.isShooting) {
                onDrawPowerBar(g, Camera.x + (w >> 1), Camera.y + h - 25 + 5, forceT1, lastForcePoint1, yPaint);
                if (PM.getMyPlayer().isDoublePower) {
                    onDrawSecondPowerBar(g, Camera.x + (w >> 1), Camera.y + h - 25 - 15 + 5, forceT2, lastForcePoint2, yPaint);
                }

                onDrawAngleBar(g, Camera.x + (w >> 1), Camera.y + h - 25 + 8, PM.getMyPlayer().angle);
            }
            PM.getMyPlayer().drawKegoc(g);
        }

        if (!pm.isYourTurn()) {
            g.translate(-g.getTranslateX(), -g.getTranslateY());
        } else if (CCanvas.isTouch) {
            g.translate(-g.getTranslateX(), -g.getTranslateY());
            if (!this.isSelectItem && MainGame.getNumberFingerOnScreen() < 2 && Camera.mode != 0 && !CPlayer.isShooting) {
                this.paintTouch(g);
            }
        }

        if (this.isSelectItem) {
            if (CCanvas.isTouch) {
                g.translate(-g.getTranslateX(), -g.getTranslateY());
                paintBorderRect(g, CCanvas.hh - 65, 4, 130, Language.chonItem());
                onDrawItem(g, CCanvas.hw - 67, CCanvas.hh - 20);
                g.drawImage(CRes.imgMenu, 25, 5, 0, false);
            } else {
                onDrawItem(g, Camera.x + (CCanvas.hw - 27), Camera.y + CCanvas.hh);
            }
        }

        this.drawSCORE(g);
        String text;
        if (PM.getCurPlayer() != null) {
            text = PM.getCurPlayer().name;
            yPaint = CCanvas.isTouch ? 25 : 0;
            if (text != null && !(PM.getCurPlayer() instanceof Boss)) {
                (PM.getCurPlayer().team ? Font.smallFontRed : Font.smallFontYellow).drawString(g, text.toUpperCase(), CScreen.w - 16, 22 + yPaint, 2);
            }
        }

        this.drawWind(g);
        this.drawUIHp(g);
        if (Camera.mode == 0) {
            this.drawWhenFreeCam(g);
        }

        if (CCanvas.currentDialog == null && !this.isSelectItem) {
            this.drawMenuCameraIcon(g);
        }

        if (!CRes.isNullOrEmpty(tfChat.getText()) && this.isChat) {
            this.isChat = false;
            if (this.chatWait == 0) {
                text = tfChat.getText();
                GameService.gI().chatToBoard(text);
                tfChat.setText("");
                this.showChat(TerrainMidlet.myInfo.IDDB, text);
                CCanvas.gameScr.showChat(TerrainMidlet.myInfo.IDDB, text, 90);
                this.chatWait = this.chatDelay;
            } else {
                tfChat.setText("");
            }

            clearKey();
        }

        this.drawChat(g);
        
        // Vẽ menu player nếu đang hiển thị
        this.drawPlayerMenu(g);
        
        if (CCanvas.isDebugging()) {
            xPaint = CCanvas.width - 2 - Font.normalRFont.getWidth(GameMidlet.version);
            yPaint = CCanvas.hieght - Font.normalRFont.getHeight() * 2;
            Font.normalRFont.drawString(g, String.valueOf(GameMidlet.timePingPaint), xPaint, yPaint, 2, false);
            Font.normalRFont.drawString(g, "CAM: " + Camera.getMode(), xPaint, yPaint - 15, 2, false);
            if (pm.isYourTurn()) {
                Font.normalRFont.drawString(g, "SHOOT: " + CPlayer.isShooting, xPaint, yPaint - 30, 2, false);
            }

            if (CCanvas.isPointerDown[0]) {
                Font.normalFont.drawString(g, CCanvas.pX[0] + "/" + CCanvas.pY[0], CCanvas.pX[0], CCanvas.pY[0] - 15, 2, false);
            }
        }

        if (CCanvas.currentDialog != null) {
            super.paintCommand(g);
        }

    }

    private void drawUIHp(mGraphics g) {
        // Vẽ UI HP theo hàng ngang gần mép trên màn hình
        if (PM.p != null&isBossNew) {
            // Tách player và boss vào 2 danh sách riêng biệt
            Vector players = new Vector();
            Vector bosses = new Vector();


            for (int i = 0; i < PM.p.length; i++) {
                if (PM.p[i] != null) {
                    if (PM.p[i].isBossNew || PM.p[i] instanceof Boss) {
                        bosses.addElement(PM.p[i]);
                    } else {
                        players.addElement(PM.p[i]);
                    }
                }
            }

            // Vị trí và kích thước siêu thu gọn
            int startY = 5; // Cực sát mép trên
            int itemHeight = 30; // Chiều cao tối thiểu
            int itemWidth = 30; // Chiều rộng rất ngắn
            int spacing = 8; // Khoảng cách tối thiểu
            int centerX = CCanvas.width / 2;

            // Tính toán vị trí căn giữa cho cả 2 bên

            // Vẽ HP của Players bên trái (căn phải)
            if (players.size() > 0) {
                int leftStartX = centerX - 40 - (itemWidth + spacing) - spacing; // Giảm khoảng cách từ giữa
                for (int i = 0; i < players.size(); i++) {
                    CPlayer player = (CPlayer) players.elementAt(i);
                    int xPos = leftStartX - (i * (itemWidth + spacing));
                    int yPos = startY;
                    if(i > 3){
                        xPos = leftStartX - (i-4) * (itemWidth + spacing);
                        yPos = startY + itemHeight + spacing+3;
                    }
                    drawPlayerHPItem(g, player, xPos, yPos, itemWidth, itemHeight, false);
                }
            }

            // Vẽ HP của Bosses bên phải (căn trái)
            if (bosses.size() > 0) {
                int rightStartX = centerX + 30; // Giảm khoảng cách từ giữa
                CPlayer boss = (CPlayer) bosses.elementAt(0);
                if(boss.gun == 32||boss.gun == 33){
                    return;
                }
                int xPos = rightStartX + (itemWidth + spacing);

                int hpPercent = 100;
                if (boss.maxhp > 0) {
                    hpPercent = (boss.hp * 100) / boss.maxhp;
                    if (hpPercent < 0) hpPercent = 0;
                    if (hpPercent > 100) hpPercent = 100;
                }
                int hpBarWidth = GameScr.imgUIBoss.image.width/2+16;
                int currentHpWidth = (hpPercent * hpBarWidth) / 100;
                g.setColor(0xFF4444); // Đỏ cho boss
                g.fillRect(xPos+58, startY+18, currentHpWidth, GameScr.imgUIBoss.image.height/3, false);
                g.drawImage(GameScr.imgUIBoss, xPos - 2, startY - 2, 0, false); // Nền boss
                mImage iconBoss = null;
                if(boss.gun == 30){
                    iconBoss = GameScr.tathan;
                } else if (boss.gun == 31) {
                    iconBoss = GameScr.nuvuong;
                } 
                if(iconBoss!= null)
                g.drawImage(iconBoss, xPos +3, startY+1 , 0, false); // Nền boss


                Font.borderFont.drawString(g,boss.name, xPos + (GameScr.imgUIBoss.image.width/2)-16, startY+2 , 2);

                Font.borderFont.drawString(g, hpPercent + "%", (xPos+GameScr.imgUIBoss.image.width/2)+30, GameScr.imgUIBoss.image.height/3+2, 1);

            }
        }
    }

    private void drawPlayerHPItem(mGraphics g, CPlayer player, int x, int y, int width, int height, boolean isBoss) {
        y+=10;
        // Vẽ nền với độ trong suốt cao hơn
        // g.setColor(0x33000000); // Đen với độ trong suốt 20%
        // g.fillRect(x, y, width, height, false);

        // Vẽ viền mỏng - màu khác nhau cho player và boss
       if (player.team) {
            g.setColor(0xFF4444); // Đỏ cho team đỏ  
        } else {
            g.setColor(0xFFDD00); // Vàng cho team vàng
        }
        g.drawRect(x, y-3, width, height, false);

        // Vẽ icon nhân vật (không hiển thị tên)
        int hpBarHeight = 3;
        int hpBarY = y + height - hpBarHeight - 2;
        // Vẽ nhân vật giống ChangePlayerCSr (dựa trên equip)
        if (player.getEquip() != null) {
            player.getEquip().paintFace(x + 12, hpBarY, 0, 0, g);
            player.getEquip().paintNon(x + 12, hpBarY, 0, 0, g);
            player.getEquip().paintKinh(x + 12, hpBarY, 0, 0, g);
        }

        // Vẽ thanh HP cực nhỏ
        int hpBarX = x + 1;
        int hpBarWidth = width - 2;

        // Nền thanh HP
        g.setColor(0x333333);
        g.fillRect(hpBarX, hpBarY-2, hpBarWidth, hpBarHeight, false);

        // Tính % HP
        int hpPercent = 100;
        if (player.maxhp > 0) {
            hpPercent = (player.hp * 100) / player.maxhp;
            if (hpPercent < 0) hpPercent = 0;
            if (hpPercent > 100) hpPercent = 100;
        }

        int currentHpWidth = (hpPercent * hpBarWidth) / 100;

        // Vẽ thanh HP
        g.setColor(0x00FF00);
        g.fillRect(hpBarX, hpBarY-2, currentHpWidth, hpBarHeight, false);

        // Viền thanh HP mỏng
        g.setColor(0x666666);
//        g.drawRect(hpBarX, hpBarY, hpBarWidth, hpBarHeight, false);

        // Highlight người chơi hiện tại với viền vàng sáng
        if (PM.curP >= 0 && PM.curP < PM.p.length && PM.p[PM.curP] == player) {
            if (CCanvas.gameTick % 40 < 20) {
                g.setColor(0xFFFF00);
                g.drawRect(x, y-3, width, height, false);
            }
        }
    }

    // Kiểm tra touch trên player HP items
    private CPlayer checkPlayerHPTouch(int x, int y) {
        if (PM.p == null || !isBossNew) {
            return null;
        }

        // Tách player và boss vào 2 danh sách riêng biệt
        Vector players = new Vector();
        Vector bosses = new Vector();

        for (int i = 0; i < PM.p.length; i++) {
            if (PM.p[i] != null) {
                if (PM.p[i].isBossNew || PM.p[i] instanceof Boss) {
                    bosses.addElement(PM.p[i]);
                } else {
                    players.addElement(PM.p[i]);
                }
            }
        }

        // Vị trí và kích thước
        int startY = 5;
        int itemHeight = 30;
        int itemWidth = 30;
        int spacing = 8;
        int centerX = CCanvas.width / 2;

        // Kiểm tra touch trên Players bên trái
        if (players.size() > 0) {
            int leftStartX = centerX - 40 - (itemWidth + spacing) - spacing;
            for (int i = 0; i < players.size(); i++) {
                CPlayer player = (CPlayer) players.elementAt(i);
                int xPos = leftStartX - (i * (itemWidth + spacing));
                int yPos = startY;
                if(i > 3){
                    xPos = leftStartX - (i-4) * (itemWidth + spacing);
                    yPos = startY + itemHeight + spacing+3;
                }
                
                // Kiểm tra touch trong vùng player
                if (x >= xPos && x <= xPos + itemWidth && y >= yPos-3 && y <= yPos + itemHeight) {
                    return player;
                }
            }
        }

        // Kiểm tra touch trên Boss bên phải
        if (bosses.size() > 0) {
            int rightStartX = centerX + 30;
            CPlayer boss = (CPlayer) bosses.elementAt(0);
            int xPos = rightStartX + (itemWidth + spacing);
            int bossWidth = GameScr.imgUIBoss.image.width/2+16;
            int bossHeight = GameScr.imgUIBoss.image.height/3;
            
            // Kiểm tra touch trong vùng boss
            if (x >= xPos - 2 && x <= xPos + bossWidth && y >= startY - 2 && y <= startY + bossHeight) {
                return boss;
            }
        }

        return null;
    }

    // Hiển thị menu player với 2 nút
    private void showPlayerMenu(CPlayer player, int x, int y) {
        this.selectedPlayer = player;
        this.isShowPlayerMenu = true;
        this.playerMenuX = x;
        this.playerMenuY = y;
    }

    // Vẽ menu player
    private void drawPlayerMenu(mGraphics g) {
        if (!isShowPlayerMenu || selectedPlayer == null) {
            return;
        }

        // Kích thước menu
        int menuWidth = 120;
        int menuHeight = 80;
        int buttonHeight = 30;
        int spacing = 5;

        // Đảm bảo menu không vượt ra ngoài màn hình
        if (playerMenuX + menuWidth > CCanvas.width) {
            playerMenuX = CCanvas.width - menuWidth - 10;
        }
        if (playerMenuY + menuHeight > CCanvas.hieght) {
            playerMenuY = CCanvas.hieght - menuHeight - 10;
        }

        // Vẽ nền menu
        g.setColor(0xCC000000); // Đen với độ trong suốt 80%
        g.fillRect(playerMenuX, playerMenuY, menuWidth, menuHeight, false);

        // Vẽ viền menu
        g.setColor(0xFFFFFFFF); // Trắng
        g.drawRect(playerMenuX, playerMenuY, menuWidth, menuHeight, false);

        // Vẽ tên player
        Font.borderFont.drawString(g, selectedPlayer.name, 
            playerMenuX + menuWidth/2, playerMenuY , mGraphics.HCENTER | mGraphics.VCENTER);

        // Vẽ nút "Xem thông tin"
        int button1Y = playerMenuY + 15;
        g.setColor(0x333333); // Xám đậm
        g.fillRect(playerMenuX + 5, button1Y, menuWidth - 10, buttonHeight, false);
        g.setColor(0xFFFFFF); // Trắng
        g.drawRect(playerMenuX + 5, button1Y, menuWidth - 10, buttonHeight, false);
        Font.borderFont.drawString(g, "Xem thông tin", 
            playerMenuX + menuWidth/2, button1Y + buttonHeight/3, mGraphics.HCENTER | mGraphics.VCENTER);

        // Vẽ nút "Kết bạn"
        int button2Y = button1Y + buttonHeight + spacing;
        g.setColor(0x333333); // Xám đậm
        g.fillRect(playerMenuX + 5, button2Y, menuWidth - 10, buttonHeight, false);
        g.setColor(0xFFFFFF); // Trắng
        g.drawRect(playerMenuX + 5, button2Y, menuWidth - 10, buttonHeight, false);
        Font.borderFont.drawString(g, "Kết bạn", 
            playerMenuX + menuWidth/2, button2Y + buttonHeight/3, mGraphics.HCENTER | mGraphics.VCENTER);
            
    }
    

    // Kiểm tra touch trên menu player
    private int checkPlayerMenuTouch(int x, int y) {
        if (!isShowPlayerMenu || selectedPlayer == null) {
            return -1;
        }

        int menuWidth = 120;
        int menuHeight = 80;
        int buttonHeight = 30;
        int spacing = 5;

        // Kiểm tra touch trong vùng menu
        if (x >= playerMenuX && x <= playerMenuX + menuWidth && 
            y >= playerMenuY && y <= playerMenuY + menuHeight) {
            
            // Đồng bộ vị trí với drawPlayerMenu - chính xác 100%
            int button1Y = playerMenuY + 15;  // Khớp với drawPlayerMenu
            int button2Y = button1Y + buttonHeight + spacing;
            
            // Vùng touch cho nút (chính xác với vùng vẽ)
            int buttonLeft = playerMenuX + 5;
            int buttonRight = playerMenuX + menuWidth - 10;
            int buttonWidth = buttonRight - buttonLeft;

            // Kiểm tra nút "Xem thông tin"
            if (x >= buttonLeft && x <= buttonRight && 
                y >= button1Y && y <= button1Y + buttonHeight) {
                return 0; // Xem thông tin
            }

            // Kiểm tra nút "Kết bạn"
            if (x >= buttonLeft && x <= buttonRight && 
                y >= button2Y && y <= button2Y + buttonHeight) {
                return 1; // Kết bạn
            }
        }

        return -1; // Không touch vào nút nào
    }

    // Xử lý hành động khi chọn menu player
    private void handlePlayerMenuAction(int action) {
        if (selectedPlayer == null) {
            return;
        }

        switch (action) {
            case 0: // Xem thông tin
                showPlayerInfo();
                break;
            case 1: // Kết bạn
                sendFriendRequest();
                break;
        }
    }

    // Hiển thị thông tin player
    private void showPlayerInfo() {
        if (selectedPlayer == null) {
            return;
        }

        //TODO: Hiển thị dialog thông tin player (cần implement dialog riêng)
    }

    // Gửi lời mời kết bạn
    private void sendFriendRequest() {
        if (selectedPlayer == null) {
            return;
        }

        // Kiểm tra không thể kết bạn với chính mình
        if (selectedPlayer.IDDB == TerrainMidlet.myInfo.IDDB) {
            this.showChat(TerrainMidlet.myInfo.IDDB, "Không thể kết bạn với chính mình!");
            return;
        }

        // Gửi lời mời kết bạn (cần implement logic gửi request qua network)
        // GameService.gI().sendFriendRequest(selectedPlayer.IDDB);
        
        // Hiển thị thông báo
        this.showChat(TerrainMidlet.myInfo.IDDB, "Đã gửi lời mời kết bạn đến " + selectedPlayer.name);
        
        // Hoặc có thể tạo dialog xác nhận
        // CCanvas.startYesNoDlg("Gửi lời mời kết bạn đến " + selectedPlayer.name + "?", 
        //     new IAction() {
        //         public void perform() {
        //             // Gửi request
        //             GameService.gI().sendFriendRequest(selectedPlayer.IDDB);
        //             CCanvas.endDlg();
        //         }
        //     }, new IAction() {
        //         public void perform() {
        //             CCanvas.endDlg();
        //         }
        //     });
    }

    private void drawWhenFreeCam(mGraphics g) {
        Font.borderFont.drawString(g, Language.cameraMode(), CCanvas.hw, CCanvas.hh - 15, 2);
        int dx = 0;
        if (CCanvas.gameTick % 10 > 4) {
            dx = 2;
        }

        g.drawImage(imgArrow, 0 + dx, CCanvas.hh, mGraphics.LEFT | mGraphics.VCENTER, false);
        g.drawRegion(imgArrow, 0, 0, imgArrow.image.getWidth(), imgArrow.image.getHeight(), 2, CCanvas.width - dx, CCanvas.hh, mGraphics.VCENTER | mGraphics.RIGHT, false);
        g.drawRegion(imgArrow, 0, 0, imgArrow.image.getWidth(), imgArrow.image.getHeight(), 5, CCanvas.hw, 25 + dx, mGraphics.TOP | mGraphics.HCENTER, false);
        g.drawRegion(imgArrow, 0, 0, imgArrow.image.getWidth(), imgArrow.image.getHeight(), 6, CCanvas.hw, CCanvas.hieght - 30 - dx, mGraphics.BOTTOM | mGraphics.HCENTER, false);
    }

    private void drawMenuCameraIcon(mGraphics g) {
        if (!CCanvas.isTouch) {
            if (Camera.mode == 0) {
                if (CCanvas.gameTick % 10 > 5) {
                    g.drawImage(CRes.imgCam, Camera.x + w - 20, Camera.y + h - 18, 0, false);
                }
            } else {
                g.drawImage(CRes.imgCam, Camera.x + w - 20, Camera.y + h - 18, 0, false);
            }
        } else {
            int hDraw = CRes.imgMenu.image.getHeight();
            if (Camera.mode == 0) {
                if (CCanvas.gameTick % 10 > 5) {
                    g.drawImage(CRes.imgCam, w - CRes.imgCam.image.getWidth() - 5, hDraw + 5, 0, false);
                }
            } else {
                g.drawImage(CRes.imgMenu, 20, hDraw + 5, 0, false);
                g.drawImage(PrepareScr.iconChat, 70, hDraw + 5, 0, false);
                g.drawImage(CRes.imgCam, w - CRes.imgCam.image.getWidth() - 5, hDraw + 5, 0, false);
            }
        }

    }

    public static void changeWind(int NextWindX, int NextWindY) {
        windx = NextWindX;
        windy = NextWindY;
        windAngle = CRes.fixangle(CRes.angle(windx, -windy));
        windPower = CRes.sqrt(windx * windx + windy * windy);
    }

    public void drawWind(mGraphics g) {
        if (Camera.mode != 0) {
            g.drawImage(wind1, CCanvas.width / 2, 22, 3, true);
            if (!this.b) {
                ++this.dem;
            } else {
                --this.dem;
            }

            if (this.dem > 5) {
                this.b = true;
            }

            if (this.dem < 0) {
                this.b = false;
            }

            g.drawImage(wind2, CCanvas.w / 2, 22, 3, true);
            if (windPower != 0) {
                int DX = 13 * CRes.cos(CRes.fixangle(windAngle)) >> 10;
                int DY = 13 * CRes.sin(CRes.fixangle(windAngle)) >> 10;
                g.drawImage(wind3, CCanvas.w / 2 + 2 + DX, 22 - DY, mGraphics.VCENTER | mGraphics.HCENTER, true);
            }

            Font.borderFont.drawString(g, String.valueOf(windPower), CCanvas.w / 2, 15, 3);
            Font.borderFont.drawString(g, Language.windAngle() + ": " + windAngle, CCanvas.w / 2, 45, 2);
        }
    }

    public void drawSCORE(mGraphics g) {
        if (!res.equals("")) {
            Font.bigFont.drawString(g, res, w / 2, 80, mGraphics.HCENTER | mGraphics.VCENTER);
            Font.borderFont.drawString(g, Language.money() + ": " + this.moneyBonus + Language.xu(), w / 2, this.moneyY, mGraphics.HCENTER | mGraphics.VCENTER);
        }

        if (this.isMoney2Fly) {
            Font.borderFont.drawString(g, "+" + this.moneyBonus2 + Language.xu(), PM.p[this.whoGetMoney2].x, this.moneyY2, mGraphics.HCENTER | mGraphics.VCENTER);
        }

    }

    public void show(CScreen lastScreen) {
        lastSCreen = lastScreen;
        CScreen.isSetClip = false;
        super.show();
    }

    public void show() {
        super.show();
        CScreen.isSetClip = false;
    }

    protected void onClose() {
        super.onClose();
        CScreen.isSetClip = true;
    }

    public static void onDrawPowerBar(mGraphics g, int x, int y, int power, int mark, int movePoint) {
        if (s_frBar != null) {
            s_frBar.drawFrame(1, x - 54 - 10, y + 5, 3, 0, g, false);
            s_frBar.drawFrame(3, x + 2 + 10, y + 5, 3, 0, g, false);
            s_frBar.fillFrame(0, x - 54 - 10, y + 5, (60 - movePoint) * 100 / 60, 3, 0, g, true);
            s_frBar.fillFrame(2, x + 2 + 10, y + 5, power * 100 / 30, 3, 0, g, true);
            s_frBar.drawFrame(5, x - 53 - 10, y - 7, 3, 0, g, true);
            s_frBar.fillFrame(4, x - 54 - 10, y - 7, PM.getMyPlayer().angryX * 100 / 100, 3, 0, g, true);
            if (mark > 0) {
                g.setColor(16482175);
                g.drawLine(x + 2 + mark * 49 / 30 + 10, y + 7, x + 2 + mark * 49 / 30 + 10, y + 7 + 7, false);
            }

        }
    }

    public static void onDrawSecondPowerBar(mGraphics g, int x, int y, int power, int mark, int movePoint) {
        if (Camera.mode != 0) {
            s_frBar.drawFrame(3, x + 2 + 10, y + 8, 0, 0, g);
            s_frBar.fillFrame(2, x + 2 + 10, y + 8, power * 100 / 30, 3, 0, g, true);
            if (mark > 0) {
                g.setColor(16482175);
                g.drawLine(x + 2 + mark * 49 / 30 + 10, y + 10, x + 2 + mark * 49 / 30 + 10, y + 8 + 9, false);
            }

        }
    }

    public static void onDrawAngleBar(mGraphics g, int x, int y, int angle) {
        if (Camera.mode != 0) {
            g.drawImage(s_imgAngle, x, y + 2, mGraphics.TOP | mGraphics.HCENTER, false);
            Font.smallFontYellow.drawString(g, "" + (angle >= 90 ? 180 - angle : angle), x, y + 4, 2);
        }
    }

    public static void onDrawItem(mGraphics g, int x, int y) {
        if (!CCanvas.isTouch) {
            g.setColor(16767817);
            g.fillRect(Camera.x, y - 1, CCanvas.width, CCanvas.isTouch ? 43 : 36, false);
        }

        Item.DrawSetItem(g, PM.getMyPlayer().item, curItemSelec, x, y, CCanvas.isTouch, PrepareScr.currLevel == 7 ? num : null);
        Font.borderFont.drawString(g, Language.use(), Camera.x + 5, Camera.y + CCanvas.hieght - Font.normalFont.getHeight() - 4, 0);
        Font.borderFont.drawString(g, Language.close(), Camera.x + CCanvas.width - 5, Camera.y + CCanvas.hieght - Font.normalFont.getHeight() - 4, 1);
    }

    public static void onDrawArrow(mGraphics g, int x, int y, int color, boolean isDynamic) {
        if (isDynamic) {
            y += 2 * (tickCount / 2 % 2);
        }

        g.setColor(color);
        g.fillRect(x, y, 11, 2, false);
        g.fillTriangle(x, y + 3, x + 11, y + 3, x + 5, y + 9, false);
    }

    public void showChat(int fromID, String text) {
        if (PrepareScr.currLevel != 7) {
            this.chatList.addElement(CCanvas.prepareScr.getPlayerNameFromID(fromID) + ": " + text);
        } else {
            this.chatList.addElement(pm.getPlayerNameFromID(fromID) + ": " + text);
        }

        if (this.chatDelay == 0) {
            this.chatDelay = this.MAX_CHAT_DELAY;
        }

    }

    public void updateChat() {
        if (this.chatDelay > 0) {
            --this.chatDelay;
            if (this.chatDelay == 0) {
                if (this.chatList.size() > 0) {
                    this.chatList.removeElementAt(0);
                }

                if (this.chatList.size() > 0) {
                    this.chatDelay = this.MAX_CHAT_DELAY;
                }
            }
        }

    }

    public void drawChat(mGraphics g) {
        if (this.chatList.size() != 0) {
            String chat = (String) this.chatList.elementAt(0);
            int nDevision = this.MAX_CHAT_DELAY - this.chatDelay;
            if (nDevision > 10) {
                nDevision = 10;
            }

            int xChat = CCanvas.width;

            for (int i = 0; i < nDevision; ++i) {
                xChat >>= 1;
            }

            Font.borderFont.drawString(g, chat, 3 + xChat, CCanvas.hieght - 14, 0);
        }
    }

    public void showChat(int fromID, String text, int Interval) {
        ChatPopup cp = new ChatPopup();
        CPlayer _player = pm.getPlayerFromID(fromID);
        if (_player != null) {
            cp.show(Interval, _player.x - Camera.x, _player.y - Camera.y - 30, text);
            CCanvas.arrPopups.addElement(cp);
        }

    }

    public void onClearMap() {
        mm.onClearMap();
        sm.onClearMap();
        System.gc();
    }

    public void onPointerPressed(int xScreen, int yScreen, int index) {
        if (Camera.mode == 1 && mSystem.currentTimeMillis() - this.timeDelayClosePauseMenu > 550L) {
            pm.onPointerPressed(xScreen, yScreen, index);
        }
        if (CCanvas.keyPressed[5]) {
            if (GameScr.pm != null && GameScr.pm.isYourTurn()) {
                PM.getMyPlayer().holdFire();
                CScreen.clearKey();
            }
        }

        super.onPointerPressed(xScreen, yScreen, index);
    }

    public void onPointerHold(int xScreen, int yScreen, int index) {
        if (!this.isSelectItem) {
            if (!this.isShowPausemenu) {
                if (mSystem.currentTimeMillis() - this.timeDelayClosePauseMenu >= 300L) {
                    if (mSystem.currentTimeMillis() - this.timeShowPauseMenu >= 300L) {
                        if (Camera.mode == 1) {
                            pm.onPointerHold(xScreen, yScreen, index);
                            int xOffset = 30;
                            int yOffset = 30;
                            if (CCanvas.isPointer(xL - trai.image.width / 2 - xOffset / 2, yL - trai.image.height / 2 - yOffset / 2, trai.image.getWidth() + xOffset, trai.image.getHeight() + yOffset, index) && pm.isYourTurn()) {
                                this.isPressXL = true;
                                return;
                            }

                            if (CCanvas.isPointer(xR - phai.image.width / 2 - xOffset / 2, yR - trai.image.height / 2 - yOffset / 2, phai.image.getWidth() + xOffset, phai.image.getHeight() + yOffset, index) && pm.isYourTurn()) {
                                this.isPressXR = true;
                                return;
                            }

                            xOffset = 30;
                            yOffset = 30;
                            if (CCanvas.isPointer(xF - xOffset / 2, yF - yOffset / 2, crossHair2.image.getWidth() + xOffset, crossHair2.image.getHeight() + yOffset, index) && pm.isYourTurn()) {
                                this.isPressXF = true;
                            }
                        }

                    }
                }
            }
        }
    }

    public void onPointerDragged(int xScreen, int yScreen, int index) {
        if (!this.isSelectItem) {
            if (!this.isShowPausemenu) {
                if (mSystem.currentTimeMillis() - this.timeDelayClosePauseMenu >= 300L) {
                    if (mSystem.currentTimeMillis() - this.timeShowPauseMenu >= 300L) {
                        try {
                            if (pm.isYourTurn() || !(PM.p[PM.curP] instanceof Boss)) {
                                if (MainGame.getNumberFingerOnScreen() >= 2) {
                                    if (Camera.mode == 1 && index == 1) {
                                        pm.onPointerDragRighCorner(xScreen, yScreen, index);
                                    }
                                } else {
                                    if (Camera.mode == 1) {
                                        pm.onPointerDrag(xScreen, yScreen, index);
                                    }

                                    if (Camera.mode == 0) {
                                        this.onDragCamera(xScreen, yScreen, index);
                                    }
                                }
                            }
                        } catch (Exception var5) {
                        }

                        super.onPointerDragged(xScreen, yScreen, index);
                    }
                }
            }
        }
    }

    public void onPointerReleased(int x, int y2, int index) {
        this.isPressXL = this.isPressXR = this.isPressXF = false;
        
        // Kiểm tra touch trên menu player trước
        if (this.isShowPlayerMenu) {
            int menuButton = checkPlayerMenuTouch(x, y2);
            if (menuButton >= 0) {
                handlePlayerMenuAction(menuButton);
                this.isShowPlayerMenu = false;
                this.selectedPlayer = null;
                return;
            } else {
                // Touch ngoài menu thì đóng menu
                this.isShowPlayerMenu = false;
                this.selectedPlayer = null;
                return;
            }
        }
        
        // Kiểm tra touch trên player HP items
        CPlayer touchedPlayer = checkPlayerHPTouch(x, y2);
        if (touchedPlayer != null && touchedPlayer != PM.p[myIndex]) {
            showPlayerMenu(touchedPlayer, x, y2);
            return;
        }
        
        if (Camera.mode == 1 && !this.isSelectItem) {
            pm.onPointerReleased(x, y2, index);
        }

        if (this.isSelectItem) {
            if (!CCanvas.isPointer(x - 50, y2 - 50, 210, 130, index)) {
                this.timeDelayClosePauseMenu = mSystem.currentTimeMillis() + 550L;
            } else {
                this.isSelectItem = true;
                this.timeDelayClosePauseMenu = mSystem.currentTimeMillis() + 550L;
                this.selectedItemPanelRealeased(x, y2, index);
            }
        } else {
            if (CCanvas.pausemenu.isShow) {
                CCanvas.pausemenu.onPointerRealeased(x, y2, index);
            } else if (CCanvas.isPointer(0, 0, 50, 50, index)) {
                this.doShowPauseMenu();
            }

            this.isShowPausemenu = CCanvas.pausemenu.isShow;
            if (CCanvas.pausemenu.isShow) {
                return;
            }

            if (CCanvas.isPointer(60, 0, 60, 60, index)) {
                this.isChat = true;
                tfChat.doChangeToTextBox();
            }

            if (PM.getCurPlayer() != null && CCanvas.isPointer(CCanvas.width - 50, 0, 50, 50, index)) {
                if (Camera.mode != 0) {
                    if (!(PM.getCurPlayer() instanceof Boss) && CCanvas.isPointer(CCanvas.width - 50, 0, 50, 50, index)) {
                        Camera.mode = 0;
                        clearKey();
                    }
                } else if (Camera.mode == 0) {
                    if (BM.active && bm.bullets.size() > 0) {
                        cam.setBulletMode((Bullet) bm.bullets.elementAt(0));
                    } else {
                        cam.setPlayerMode(PM.curP);
                    }
                }
            }
        }

    }

    public void onPaintSliderRightConer(mGraphics gr, int xPaint, int yPaint) {
    }

    public void notClearMap(int indexClear) {
        System.gc();
    }
}
