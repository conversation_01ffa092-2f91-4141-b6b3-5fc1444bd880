package screen;

import CLib.Image;
import CLib.mFont;
import CLib.mGraphics;
import CLib.mImage;
import Equipment.Equip;
import Equipment.PlayerEquip;
import coreLG.CCanvas;
import coreLG.TerrainMidlet;
import model.*;

import java.util.ArrayList;
import java.util.HashMap;

import network.Command;
import network.GameService;
// import player.CPlayer; // Not used in PetScreen

import java.util.Vector;

/**
 * Created by khiem on 9/16/2025.
 *
 * <AUTHOR>
 */
public class PetScreen extends TabScreen {
    int num;
    int select;
    int[] xE;
    int[] yE;
    byte[] typeE;
    int[] dbKeyChange = new int[4];
    boolean isSelect;
    // Use EquipScreen's inventory instead of having separate inventory
    // public static Vector<Equip> inventory = new Vector();
    public Vector<Equip> myEquips = new Vector();
    public static mImage imgIcon;
    public static mImage imgMaterial;
    public static mImage[] imgIconEQ = new mImage[5];
    public int wTab;
    public int wIndex;
    public int hIndex;
    public int wP;
    public Command cmdSelect;
    public Command menu;
    public static boolean isEquip = false;
    int dem;
    boolean isCompine;
    short mSelect;
    short mComSelect;
    public short[] lastDb = new short[4];
    public Vector vLastE = new Vector();
    public Equip[] lastEquip = new Equip[4];
    int W;
    static int cmtoYI;
    static int cmyI;
    static int cmdyI;
    static int cmvyI;
    int nLine;
    int ind;
    public byte[] addPoint;
    public int[] atts;
    public Equip equipSelect;
    int dx;
    String attribute;
    String name;
    int xName;
    int wName;
    int wDetail;
    boolean scroll;
    int ds;
    Position transText1;
    Position transText2;
    int cc;
    int ee;
    public Equip[] currEq;
    PlayerEquip equip;
    int pa;
    boolean trans;
    int speed;
    int cmtoYITem;
    public int frame;
    public HashMap<Integer, Long> imgRequest = new HashMap<>();

    // Skills system
    public Skill[] skills;
    public int selectedSkill = -1;

    public PetScreen() {
        this.W = CCanvas.width;
        this.addPoint = new byte[6];
        this.atts = new int[5];
        this.skills = new Skill[5];
        this.dx = -1;
        this.attribute = "";
        this.name = "";
        this.transText1 = new Position(0, 1);
        this.transText2 = new Position(0, 1);
        this.currEq = new Equip[4];
        this.equip = null;
        this.pa = 0;
        this.trans = false;
        this.speed = 1;
        this.nameCScreen = "PetScreen screen!";

        // Tăng kích thước 30% so với EquipScreen
        int baseWidth = 150;
        int baseHeight = 170;
        this.wTabScreen = (int) (baseWidth * 1.3); // 195
        this.hTabScreen = (int) (baseHeight * 1.3); // 221

        this.xPaint = CCanvas.width / 2 - this.wTabScreen / 2;
        this.yPaint = (CCanvas.hieght - CScreen.cmdH) / 2 - this.hTabScreen / 2;
        // Căn lại vị trí pet slots cho kích thước mới (tăng 30%)
        int centerX = this.W / 2;
        int offsetY = (int) (1.3 * 33); // Tăng offset 30%

        this.xE = new int[]{
                centerX - (int) (60 * 1.3), // Pet slot 0 - center top
                centerX - (int) (80 * 1.3), // Pet slot 1 - left bottom
                centerX - (int) (30 * 1.3), // Pet slot 2 - right bottom
                centerX - (int) (55 * 1.3) // Pet slot 3 - center bottom
        };
        this.yE = new int[]{
                this.yPaint + offsetY, // Pet slot 0 - top
                this.yPaint + offsetY* 3 - 20, // Pet slot 1 - bottom left
                this.yPaint + offsetY * 3 - 20, // Pet slot 2 - bottom right
                this.yPaint + offsetY  // Pet slot 3 - center bottom
        };
        this.typeE = new byte[]{0, 1, 2, 3};
        this.menu = new Command("Menu", new IAction() {
            public void perform() {
                Command detail = new Command(Language.detail(), new IAction() {
                    public void perform() {
                        PetScreen.this.doDetail();
                    }
                });
                Command inventory = new Command(Language.ruongdo(), new IAction() {
                    public void perform() {
                        if (EquipScreen.inventory.size() == 0) {
                            CCanvas.startOKDlg(Language.beNotInventory());
                        } else {
                            PetScreen.this.doInventory();
                        }
                    }
                });
                Vector<Command> menu = new Vector();
                menu.addElement(inventory);
                menu.addElement(detail);
                CCanvas.menu.startAt(menu, 0);
            }
        });
        this.left = this.menu;
        this.cmdSelect = new Command(Language.select(), new IAction() {
            public void perform() {
                PetScreen.this.doFire();
            }
        });
        this.center = this.cmdSelect;
        this.right = new Command(Language.close(), new IAction() {
            public void perform() {
                // Pet system không cần save confirmation vì không dùng dbKey
                PetScreen.this.doClose();
            }
        });
        this.title = "Thú Nuôi";
        this.n = 4;
        this.getW();
        if (CCanvas.isTouch) {
            this.wTab = (int) (30 * 1.3); // 39
            this.wIndex = 2;
            this.wP = (int) (5 * 1.3); // 6
        } else {
            this.wTab = (int) (20 * 1.3); // 26
            this.wIndex = 3;
            this.wP = 0;
        }

    }

    public void init() {
        this.n = 6;
        isEquip = true;
        this.isClose = false;
        this.select = 0;
        PlayerInfo m = TerrainMidlet.myInfo;
        PlayerInfo.vipID = m.equipVipID[m.gun][1];
        this.getLastEquip();
        this.getDetail();
        TerrainMidlet.myInfo.getMyEquip(9);
        TerrainMidlet.myInfo.setAllEquipEffect();

        // Khởi tạo pet array nếu chưa có
        PlayerInfo info = TerrainMidlet.myInfo;
        if (info.pet == null) {
            info.pet = new Equip[4]; // Tạo 4 pet slots (sẽ chứa material items)
        }

        this.getMyEquip();

        this.vLastE = this.myEquips;
        this.setCurrEquip();
        this.getBaseAttribute();
        this.seeNextAttribute();
        this.initSkills();
    }

    public void show(CScreen lastScreen) {
        if (this.select < 0) {
            this.select = 0;
        }

        if (this.select > this.myEquips.size() - 1) {
            this.select = this.myEquips.size() - 1;
        }

        if (this.getEquipSelect() != null) {
            this.getDetail();
        }

        super.show(lastScreen);
    }

    public void getMyEquip() {
        this.myEquips.removeAllElements();

        for (int i = 0; i < EquipScreen.inventory.size(); ++i) {
            Equip e = (Equip) EquipScreen.inventory.elementAt(i);
            // Pet items are materials with type 0-3
            if (e.isMaterial && e.index >= 0 && e.index <= 3) {
                this.myEquips.addElement(e);
            }
        }

        int materialId = -1;

        for (int i = 0; i < EquipScreen.inventory.size(); ++i) {
            Equip e = (Equip) EquipScreen.inventory.elementAt(i);
            if (e.isMaterial && materialId != e.id && e.index >= 0 && e.index <= 3) {
                materialId = e.id;
                if (e.materialIcon == null) {
                    if (MaterialIconMn.isExistIcon(e.icon)) {
                        e.materialIcon = MaterialIconMn.getImageFromID(e.icon);
                    }

                    GameService.gI().getMaterialIcon((byte) 0, materialId, -1);
                }
            }
        }

        this.hIndex = this.myEquips.size() / this.wIndex;
        if (this.myEquips.size() % this.wIndex != 0) {
            ++this.hIndex;
        }

        // PlayerInfo info = TerrainMidlet.myInfo;

        // for (int i = 0; i < this.myEquips.size(); ++i) {
        // Equip tam = (Equip) this.myEquips.elementAt(i);
        // // For pets, we check if the pet type is within valid range (0-3) and
        // equipped
        // // Pet không cần kiểm tra ability vì không sử dụng PlayerEquip system
        // // Pet abilities được xử lý trực tiếp
        // if (tam.index >= 0 && tam.index < 4 && info.myEquip.pet[tam.index] != null) {
        // // Pet system không cần removeAbility/addAbilityFromEquip
        // // Abilities được tính toán trực tiếp từ pet
        // }
        // }

    }

    public void getMaterialIcon(int id, byte[] dataRawImages, int len) {
        int i;
        Equip e;
        for (i = 0; i < EquipScreen.inventory.size(); ++i) {
            e = (Equip) EquipScreen.inventory.elementAt(i);
            if (e.isMaterial && e.id == id) {
                e.materialIcon = mImage.createImage(dataRawImages, 0, len, "EquipScrenn" + e.id, (String) null);
            }
        }

        for (i = 0; i < this.myEquips.size(); ++i) {
            e = (Equip) this.myEquips.elementAt(i);
            if (e.isMaterial && e.id == id) {
                e.materialIcon = mImage.createImage(dataRawImages, 0, len, "EquipScrenn" + e.id, (String) null);
            }
        }

    }

    public void getMaterialIcon(int id, Image img) {
        int i;
        Equip e;
        for (i = 0; i < EquipScreen.inventory.size(); ++i) {
            e = (Equip) EquipScreen.inventory.elementAt(i);
            if (e.isMaterial && e.id == id) {
                e.materialIcon = new mImage(img);
            }
        }

        for (i = 0; i < this.myEquips.size(); ++i) {
            e = (Equip) this.myEquips.elementAt(i);
            if (e.isMaterial && e.id == id) {
                e.materialIcon = new mImage(img);
            }
        }

    }

    public void addEquip(Equip e) {
        EquipScreen.inventory.insertElementAt(e, 0);
        this.myEquips.insertElementAt(e, 0);
    }

    public void doClose() {
        this.isClose = true;
        CCanvas.endDlg();
//        this.resetEquip();
    }

    public void doAgree() {
        // Pet system không cần gửi changeEquip request vì không dùng dbKey
        // Pets được quản lý local
        CCanvas.endDlg();
    }

    public void doFire() {
        try {
            if (this.myEquips.size() == 0) {
                return;
            }

            if (this.getEquipSelect() == null) {
                return;
            }

            // For pet system, we allow material items to be equipped
            // Remove the material check since pets are materials

            Equip e = this.getEquipSelect();

            // Pet không sử dụng dbKey để kiểm tra, có thể equip cùng pet nhiều lần
            // hoặc kiểm tra theo ID nếu cần
            // int i;
            // for (i = 0; i < 4; ++i) {
            // if (m.myEquip.equips[i] != null && e.id == m.myEquip.equips[i].id) {
            // return; // Nếu muốn ngăn equip cùng pet
            // }
            // }

            // Sử dụng index của pet trong danh sách thay vì type
            int petIndex = e.index; // index của pet được chọn trong myEquips
            if (petIndex >= 0 && petIndex < this.myEquips.size()) {
                // Pet sử dụng index để equip vào slot tương ứng (0-3)
                int slotIndex = petIndex % 4; // đảm bảo chỉ có 4 slots

                // Pet không sử dụng dbKey, chỉ cần thay đổi pet hiện tại
                // Lưu pet vào slot tương ứng
                this.equipPetToSlot(e, slotIndex);
                this.setCurrEquip();
                this.getBaseAttribute();
            }
        } catch (Exception var5) {
        }

    }

    public void doDetail() {
        CCanvas.startOKDlg(this.attribute);
    }

    public Equip getEquipSelect() {
        if (this.myEquips.size() > 0) {
            if (this.select <= 0) {
                this.select = 0;
            } else if (this.select >= this.myEquips.size()) {
                this.select = this.myEquips.size() - 1;
            }

            Equip eS = (Equip) this.myEquips.elementAt(this.select);
            return eS;
        } else {
            return null;
        }
    }

    public void resetEquip() {
        try {
            PlayerInfo m = TerrainMidlet.myInfo;

            for (int i = 0; i < this.lastEquip.length; ++i) {
                if (this.lastEquip[i] != null) {
                    m.pet[i] = this.lastEquip[i]; // Direct assignment for material pets
                }
            }

            this.myEquips = this.vLastE;
        } catch (Exception var3) {
        }

    }

    public void getLastEquip() {
        PlayerInfo m = TerrainMidlet.myInfo;

        for (int i = 0; i < 4; ++i) {
            if (m.pet[i] != null) {
                // Pet lưu trực tiếp, không qua equipID
                this.lastDb[i] = (short) m.pet[i].id;
                // Pet không cần lưu vào m.equipID vì không dùng PlayerEquip
                // m.equipID[m.gun][i] = (short) m.myEquip.equips[i].id;
                this.lastEquip[i] = m.pet[i]; // Direct reference for material pets
            } else {
                this.lastDb[i] = -1;
            }

            // Pet không cần dbKeyChange
            this.dbKeyChange[i] = this.lastDb[i];
        }

    }

    public void equipPetToSlot(Equip pet, int slotIndex) {
        GameService.gI().useItemPet(pet.indexUI);
    }

    public void changeEquip() {
        // Method này giờ được thay thế bởi equipPetToSlot cho pet system
        // Giữ lại để tương thích
        Equip sl = this.getEquipSelect();
        if (sl != null) {
            int slotIndex = sl.index % 4;
            this.equipPetToSlot(sl, slotIndex);
        }
    }

    public void doInventory() {
        this.isClose = true;
        CCanvas.inventory.show(CCanvas.menuScr);
    }

    public void paintEquip(mGraphics g, Image img, int X, int Y) {
        g.setColor(4156571);
        g.fillRoundRect(X - 1 - 9, Y - 1 - 9, 20, 20, 4, 4, false);
        g.setColor(16774532);
        g.fillRect(X - 1 - 8, Y - 1 - 8, 18, 18, false);
    }

    public void itemCamera() {
        if (cmyI != cmtoYI) {
            cmvyI = cmtoYI - cmyI << 2;
            cmdyI += cmvyI;
            cmyI += cmdyI >> 4;
            cmdyI &= 15;
        }

        this.nLine = this.num / this.wIndex;
        if (this.num % this.wIndex != 0) {
            ++this.nLine;
        }

        int yLim = this.nLine * this.wTab - 60;
        if (cmyI > yLim) {
            cmyI = yLim;
        }

        if (cmyI < 0) {
            cmyI = 0;
        }

    }

    public void paintItem(mGraphics g, int X, int Y) {
        g.setColor(4156571);
        // Tăng kích thước pet inventory area 30%
        int inventoryWidth = (int) (72 * 1.3);
        int inventoryHeight = (int) (67 * 1.3);
        int inventoryY = this.yPaint + (int) (96 * 1.3);
        g.fillRoundRect(X - (int) (5 * 1.3) - 10, inventoryY, inventoryWidth, inventoryHeight, 6, 6, false);
        // Tăng clip area 30%
        int clipWidth = (int) (62 * 1.3);
        int clipHeight = (int) (60 * 1.3);
        g.setClip(X - 1 - 10, Y - 1, clipWidth, clipHeight);
        g.translate(0, -cmyI);
        int j = 0;
        int i = 0;
        Equip e = null;

        for (int n = 0; n < this.myEquips.size(); ++n) {
            e = (Equip) this.myEquips.elementAt(n);
            // Điều chỉnh vị trí item để căn chỉnh với nền đã tăng kích thước
            int x1 = X + i * (int) (this.wTab * 1.3) + (int) (this.wP * 1.3) - 10;
            int y1 = Y + j * (int) (this.wTab * 1.3) + (int) (this.wP * 1.3);
            int xIcon = x1;
            int yIcon = y1;
            if (e != null) {
                PlayerInfo m = TerrainMidlet.myInfo;
                // Kiểm tra pet đã equipped theo ID trong pet array
                boolean isEquipped = false;
                for (int slot = 0; slot < 4; slot++) {
                    if (m.pet[slot] != null && e.id == m.pet[slot].id) {
                        isEquipped = true;
                        break;
                    }
                }
                // Tăng kích thước icon và highlight 30%
                int iconSize = (int) (16 * 1.3);
                int highlightSize = (int) (18 * 1.3);

                if (isEquipped) {
                    g.setColor(4819660);
                    g.fillRect(x1, y1, iconSize, iconSize, true);
                }

                if (this.select == n) {
                    g.setColor(16767817);
                    g.fillRect(x1 - 1, y1 - 1, highlightSize, highlightSize, true);
                    if (!CCanvas.isTouch) {
                        cmtoYI = y1 - (Y + (int) (20 * 1.3));
                    }

                    Equip eS = (Equip) this.myEquips.elementAt(this.select);
                    if (e != null) {
                        for (int a = 0; a < this.typeE.length; ++a) {
                            if (eS.index == this.typeE[a]) {
                                this.ind = a;
                            }
                        }
                    }
                }

                if (e.isSelect) {
                    g.setColor(16777215);
                    g.fillRect(x1, y1, iconSize, iconSize, true);
                }

                e.drawIcon(g, x1, y1, true);
                if (e.isMaterial) {
                    // Điều chỉnh vị trí và kích thước các chấm slot
                    int slotDotSize = (int) (2 * 1.3);
                    int slotSpacing = (int) (4 * 1.3);
                    for (int a = 0; a < 3 - e.slot; ++a) {
                        if (i != this.select) {
                            g.setColor(16377901);
                            g.fillRect(xIcon + a * slotSpacing, yIcon, slotDotSize, slotDotSize, true);
                        } else {
                            g.setColor(0);
                            g.fillRect(xIcon + a * slotSpacing, yIcon, slotDotSize, slotDotSize, true);
                        }
                    }
                }
            }

            ++i;
            if (i == this.wIndex) {
                ++j;
                i = 0;
            }
        }

        g.translate(0, -g.getTranslateY());
    }

    public void getBaseAttribute() {
        PlayerInfo info = TerrainMidlet.myInfo;
        int[] ability = new int[5];
        int j;
        Equip eq;

        for (j = 0; j < 4; ++j) {
            eq = info.pet[j]; // Sử dụng pet array thay vì equips array
            if (eq != null) {
                // Pet abilities được tính trực tiếp từ pet array
                for (int k = 0; k < 5; ++k) {
                    ability[k] += eq.inv_ability[k];
                }
            }
        }
        this.atts = ability;
    }

    public void paintAbility(mGraphics g) {
        // Tăng vị trí ability display 30%
        int levelX = this.W / 2 + (int) (24 * 1.3);
        int levelY = this.yPaint + (int) (22 * 1.3);
        int percentX = this.W / 2 + (int) (75 * 1.3);

        Pet p = TerrainMidlet.myInfo.myPet;
        Font.normalFont.drawString(g, "Level: " + (p != null ? p.level : ""), levelX, levelY, 3);
        Font.normalFont.drawString(g, (p != null ? p.percernt : "") + "%", percentX, levelY, 3);

        for (int i = 0; i < 5; ++i) {
            // Tăng vị trí ability icons và bars 30%
            int abilityIconX = this.W / 2 - 1;
            int abilityIconY = this.yPaint + (int) ((46 + i * 18) * 1.3);
            int abilityBarY = this.yPaint + (int) ((38 + i * 18) * 1.3);

            g.drawRegion(LevelScreen.ability, 0, i * 16, 16, 16, 0, abilityIconX, abilityIconY, 3, false);
            g.setColor(2378093);
            g.fillRect(CCanvas.width / 2 + (int) (9 * 1.3), abilityBarY, (int) (35 * 1.3), (int) (16 * 1.3), false);
            g.fillRect(CCanvas.width / 2 + (int) (46 * 1.3), abilityBarY, (int) (18 * 1.3), (int) (16 * 1.3), false);
            g.fillRect(CCanvas.width / 2 + (int) (66 * 1.3), abilityBarY, (int) (19 * 1.3), (int) (16 * 1.3), false);
            PlayerInfo info = TerrainMidlet.myInfo;
            String attAddP = String.valueOf(Math.abs(info.attAddPoint1[i]));
            String perAddP = String.valueOf(Math.abs(info.attAddPoint2[i]));
            int attribute = this.atts[i];
            Font.normalYFont.drawString(g, String.valueOf(attribute), this.W / 2 + (int) (26 * 1.3), abilityBarY + 1,
                    3);
            byte var10000 = info.UpOrDown1[i];
            info.getClass();
            if (var10000 == 0) {
                Font.normalYFont.drawString(g, attAddP, this.W / 2 + (int) (56 * 1.3), abilityBarY + 1, 3);
            }

            var10000 = info.UpOrDown1[i];
            info.getClass();
            if (var10000 == 2) {
                Font.normalRFont.drawString(g, attAddP, this.W / 2 + (int) (56 * 1.3), abilityBarY + 1, 3);
            }

            var10000 = info.UpOrDown1[i];
            info.getClass();
            if (var10000 == 1) {
                Font.normalGFont.drawString(g, attAddP, this.W / 2 + (int) (56 * 1.3), abilityBarY + 1, 3);
            }

            var10000 = info.UpOrDown2[i];
            info.getClass();
            if (var10000 == 0) {
                Font.normalYFont.drawString(g, perAddP, this.W / 2 + (int) (75 * 1.3), abilityBarY + 1, 3);
            }

            var10000 = info.UpOrDown2[i];
            info.getClass();
            if (var10000 == 2) {
                Font.normalRFont.drawString(g, perAddP, this.W / 2 + (int) (75 * 1.3), abilityBarY + 1, 3);
            }

            var10000 = info.UpOrDown2[i];
            info.getClass();
            if (var10000 == 1) {
                Font.normalGFont.drawString(g, perAddP, this.W / 2 + (int) (75 * 1.3), abilityBarY + 1, 3);
            }
        }

    }

    public void getDetail() {
        this.dx = -1;
        this.ds = 0;
        this.scroll = false;
        this.attribute = "";
        Equip eq = this.getEquipSelect();
        if (eq != null) {
            this.xName = this.W / 2 - 4;
            this.wName = Font.normalFont.getWidth(eq.name);
            this.name = eq.name;
            this.attribute = eq.getStrInvDetailPet();

            this.wDetail = Font.normalFont.getWidth(this.name);
        }
    }

    public Position transTextLimit(Position pos, int limit) {
        pos.x += pos.y;
        if (pos.y == -1 && Math.abs(pos.x) > limit) {
            pos.y *= -1;
        }

        if (pos.y == 1 && pos.x > 5) {
            pos.y *= -1;
        }

        return pos;
    }

    public void initSkills() {
        if( TerrainMidlet.myInfo == null || TerrainMidlet.myInfo.myPet == null || TerrainMidlet.myInfo.myPet.skills == null) {
            return;
        }
        this.skills = TerrainMidlet.myInfo.myPet.skills;
    }

    public void paintSkills(mGraphics g) {
        // Sử dụng kích thước và vị trí giống như money area cũ
        int skillAreaX = this.W / 2 - (int) (9 * 1.3);
        int skillAreaY = this.yPaint + (int) ((35 + 90) * 1.3);
        int skillAreaWidth = (int) (95 * 1.3);
        int skillAreaHeight = (int) (60 * 1.3);
        int skillSlotSize = (int) (16 * 1.3); // Nhỏ hơn để vừa trong khu vực
        int skillSpacing = (int) (4 * 1.3); // Giảm khoảng cách

        // Không vẽ nền, chỉ vẽ skills
        g.setClip(skillAreaX, skillAreaY, skillAreaWidth, skillAreaHeight);

        // Draw 5 skill slots in a row
        for (int i = 0; i < 5 && i < this.skills.length; i++) {
            Skill skill = this.skills[i];
            int slotX = skillAreaX + (int) (2 * 1.3) + i * (skillSlotSize + skillSpacing)
                    - (i == 0 ? 2 : i == 4 ? -1 : 0);
            int slotY = skillAreaY + (int) (5 * 1.3);

            if (skill != null && skill.icon != null) {
                g.drawImage(skill.icon, slotX, slotY, 0, false);
            } else {
                g.setColor(0x000000);
                g.fillRoundRect(slotX, slotY, skillSlotSize, skillSlotSize, 3, 3, false);
            }

            // Draw skill slot border
            if (this.selectedSkill == i) {
                g.setColor(0xFFFF00); // Yellow border for selected
                g.drawRoundRect(slotX - 1, slotY - 1, skillSlotSize + 2, skillSlotSize + 2, 3, 3, false);
            } else {
                g.setColor(0x000000); // Black border for normal
                g.drawRoundRect(slotX, slotY, skillSlotSize, skillSlotSize, 3, 3, false);
            }

        }

        // Không hiển thị skill detail text ở đây để tránh đè lên name item
        // Detail sẽ được hiển thị trong dialog khi click
        g.setColor(1521982);
        int moneyX = this.W / 2 - (int) (9 * 1.3);
        int moneyY = this.yPaint + (int) ((40 + 90) * 1.3);
        int moneyWidth = (int) (95 * 1.3);
        int moneyRowHeight = (int) (16 * 1.3);
        g.fillRoundRect(moneyX, moneyY + (int) (18 * 1.3), moneyWidth, moneyRowHeight, 6, 6, false);
        int xName = this.W / 2;
        int yName = this.yPaint + 79 + 117;
        Font.normalGFont.drawString(g, this.name, xName + this.dx + this.ee, yName, 0);
        g.setClip(0, 0, CCanvas.width, CCanvas.hieght);
    }

    public void handleSkillClick(int xPress, int yPress, int index) {
        // Calculate skill area coordinates (same as in paintSkills)
        int skillAreaX = this.W / 2 - (int) (9 * 1.3);
        int skillAreaY = this.yPaint + (int) ((35 + 90) * 1.3);
        int skillSlotSize = (int) (16 * 1.3);
        int skillSpacing = (int) (4 * 1.3);

        // Check if click is within skills area
        for (int i = 0; i < 5 && i < this.skills.length; i++) {
            int slotX = skillAreaX + (int) (2 * 1.3) + i * (skillSlotSize + skillSpacing)
                    - (i == 0 ? 2 : i == 4 ? -1 : 0);
            int slotY = skillAreaY + (int) (5 * 1.3);
            if (CCanvas.isPointer(slotX, slotY, skillSlotSize, skillSlotSize, index)) {
                this.selectedSkill = i;
                Skill clickedSkill = this.skills[i];
                if( clickedSkill != null && clickedSkill.detail != null)
                CCanvas.startOKDlg(clickedSkill.detail);
                break;
            }
        }
    }

    public void setCurrEquip() {
        for (int i = 0; i < 4; ++i) {
            PlayerInfo info = TerrainMidlet.myInfo;

            // Pet không sử dụng PlayerEquip, currEq trực tiếp là pet hiện tại
            this.currEq[i] = info.pet[i]; // Trực tiếp reference đến pet
        }
    }

    public void paintPlayer(mGraphics g) {
        if (this.ind != 0) {
            g.setColor(16767817);
            g.drawRect(this.xE[this.ind] - 9, this.yE[this.ind] - 9, 17, 17, false);
            g.drawRect(this.xE[this.ind] - 10, this.yE[this.ind] - 10, 19, 19, false);
            g.setColor(1521982);
            g.drawRect(this.xE[this.ind] - 11, this.yE[this.ind] - 11, 21, 21, false);
        }

        for (int i = 0; i < 4; ++i) {
            if (i == 0) {
                continue;
            }
            this.paintEquip(g, GameScr.s_imgITEM.image, this.xE[i], this.yE[i]);
            if (this.currEq[i] != null) {
                // Pet được vẽ trực tiếp, không qua PlayerEquip
                if (MaterialIconMn.isExistIcon(this.currEq[i].icon)) {
                    mImage img = MaterialIconMn.getImageFromID(this.currEq[i].icon);
                    g.drawImage(img, this.xE[i] , this.yE[i] , 3, false);
                } else {
                    long time = imgRequest.getOrDefault(this.currEq[i].id, 0L);
                    if (System.currentTimeMillis() - time > 60000) {
                        imgRequest.put(this.currEq[i].id, System.currentTimeMillis());
                        GameService.gI().getMaterialIcon((byte) 0, this.currEq[i].id, -1);
                    }
                }

            } else {
                // Vẽ slot trống cho pet
                g.drawRegion(GameScr.s_imgITEM, 0, 0, 16, 16, 0, this.xE[i], this.yE[i], 3, true);
            }
        }

        PlayerInfo myInfo = TerrainMidlet.myInfo;
        // Pet screen không cần paint player
        if (TerrainMidlet.isVip[myInfo.gun]) {
            this.equip = myInfo.myVipEquip;
        } else {
            this.equip = myInfo.myEquip;
        }
        //
        // CPlayer.paintSimplePlayer(gun, CCanvas.gameTick % 5 > 2 ? 5 : 4,
        // CCanvas.width / 2 - 52, this.yPaint + 71, 0,
        // this.equip, g);
    }

    public void paint(mGraphics g) {
        super.paint(g);
        this.paintPet(g);
        this.paintPlayer(g);
        // Căn lại vị trí pet inventory cho kích thước mới
        int itemX = this.W / 2 - (int) (78 * 1.3);
        int itemY = this.yPaint + (int) (102 * 1.3);
        this.paintItem(g, itemX, itemY);
        this.paintSkills(g);
        this.paintAbility(g);
        this.paintSuper(g);
    }

    public void paintPet(mGraphics g) {
        Pet p = TerrainMidlet.myInfo.myPet;
        if (p != null && p.imgPetMap != null) {
            if (CCanvas.gameTick % p.tickMove == 0) {
                this.frame++;
                if (frame < p.frameStartStand || frame > p.frameEndStand) {
                    frame = p.frameStartStand;
                }
            }
            mImage img = p.getImage(frame);
            if (img != null) {
                g.drawImage(img, this.xE[0] + p.dxMove, this.yE[0] + p.dyMove, 3, false);
            }
        }
    }

    public void seeNextAttribute() {
        if (this.myEquips.size() != 0) {
            try {
                PlayerInfo m = TerrainMidlet.myInfo;
                Equip e = this.getEquipSelect();
                if (e == null) {
                    return;
                }

                Equip currE = null;
                // Pet không sử dụng PlayerEquip.getEquip
                currE = m.pet[e.index]; // Trực tiếp lấy pet hiện tại

                m.compareEquipPet(e, currE);
                this.getDetail();
                this.transText2.x = -1;
            } catch (Exception var4) {
                var4.printStackTrace();
            }

        }
    }

    public void update() {
        super.update();
        this.num = this.myEquips.size();
        this.center = this.cmdSelect;
        this.left = this.menu;
        this.itemCamera();
        PlayerInfo m = TerrainMidlet.myInfo;
        PlayerInfo.vipID = m.equipVipID[m.gun][1];
        String money = CRes.getMoneys(TerrainMidlet.myInfo.xu) + Language.xu() + "-" + TerrainMidlet.myInfo.luong
                + Language.luong2();
        int bb = Font.normalFont.getWidth(money);
        if (bb > 85) {
            this.transTextLimit(this.transText1, bb - 80);
        }

        this.cc = this.transText1.x;
        int dd = Font.normalFont.getWidth(this.name);
        if (dd > 85) {
            this.transTextLimit(this.transText2, dd - 80);
        }

        this.ee = this.transText2.x;
    }

    public void onPointerDragged(int xDrag, int yDrag, int index) {
        super.onPointerDragged(xDrag, yDrag, index);
        if (!this.trans) {
            this.pa = cmyI;
            this.trans = true;
        }

        if (CCanvas.isPc()) {
            this.speed = 3;
        }

        cmtoYI = this.pa + (CCanvas.pyFirst[index] - yDrag) * this.speed;
        this.cmtoYITem = this.pa + (CCanvas.pyFirst[index] - yDrag);
        if (cmtoYI <= 0) {
            cmtoYI = 0;
            this.cmtoYITem = 0;
        }

    }

    public void onPointerReleased(int xReleased, int yReleased, int index) {
        super.onPointerReleased(xReleased, yReleased, index);
        this.trans = false;
        // Tính toạ độ và vùng click khớp với paintItem (tỷ lệ 1.3 và offset -10)
        int xDraw = this.W / 2 - (int) (78 * 1.3);
        int yDraw = this.yPaint + (int) (102 * 1.3);
        int areaX = xDraw - 1 - 10;
        int areaY = yDraw - 1;
        int areaW = (int) (62 * 1.3);
        int areaH = (int) (60 * 1.3);
        if (CCanvas.isPointer(areaX, areaY, areaW, areaH, index)) {
            int cellSize = (int) (this.wTab * 1.3);
            int padding = (int) (this.wP * 1.3);
            int scrollOffset = this.trans ? this.cmtoYITem : cmyI;
            int localY = (scrollOffset + yReleased) - (yDraw + padding);
            int localX = xReleased - (xDraw + padding - 10);
            if (localX < 0) localX = 0;
            if (localY < 0) localY = 0;
            int col = localX / cellSize;
            int row = localY / cellSize;
            if (col < 0) col = 0;
            if (row < 0) row = 0;
            if (col >= this.wIndex) col = this.wIndex - 1;
            int maxRows = (this.myEquips.size() + this.wIndex - 1) / this.wIndex;
            if (row >= maxRows) row = maxRows - 1;
            int aa = row * this.wIndex + col;
            if (aa == this.select && this.center != null && CCanvas.isDoubleClick) {
                this.center.action.perform();
            }

            this.select = aa;
            this.getDetail();
            if (this.select < 0) {
                this.select = 0;
            }

            if (this.select > this.myEquips.size() - 1) {
                this.select = this.myEquips.size() - 1;
            }
        }

        this.seeNextAttribute();
    }

    public void onPointerPressed(int xPress, int yPress, int index) {
        super.onPointerPressed(xPress, yPress, index);

        // Check for skill clicks first, only if click is in skill area
        int skillAreaX = this.W / 2 - (int) (9 * 1.3);
        int skillAreaY = this.yPaint + (int) ((35 + 90) * 1.3);
        int skillAreaWidth = (int) (95 * 1.3);
        int skillAreaHeight = (int) (25 * 1.3); // Chỉ phần skills, không bao gồm name area

        if (CCanvas.isPointer(skillAreaX, skillAreaY, skillAreaWidth, skillAreaHeight, index)) {
            this.handleSkillClick(xPress, yPress, index);
            return; // Ngăn xử lý item click nếu đã click skill
        }

        // Vùng click khớp với paintItem (tỷ lệ 1.3 và offset -10)
        int xDraw = this.W / 2 - (int) (78 * 1.3);
        int yDraw = this.yPaint + (int) (102 * 1.3);
        int areaX = xDraw - 1 - 10;
        int areaY = yDraw - 1;
        int areaW = (int) (62 * 1.3);
        int areaH = (int) (60 * 1.3);
        if (CCanvas.isPointer(areaX, areaY, areaW, areaH, index)) {
            int cellSize = (int) (this.wTab * 1.3);
            int padding = (int) (this.wP * 1.3);
            int scrollOffset = this.trans ? this.cmtoYITem : cmyI;
            int localY = (scrollOffset + yPress) - (yDraw + padding);
            int localX = xPress - (xDraw + padding - 10);
            if (localX < 0) localX = 0;
            if (localY < 0) localY = 0;
            int col = localX / cellSize;
            int row = localY / cellSize;
            if (col < 0) col = 0;
            if (row < 0) row = 0;
            if (col >= this.wIndex) col = this.wIndex - 1;
            int maxRows = (this.myEquips.size() + this.wIndex - 1) / this.wIndex;
            if (row >= maxRows) row = maxRows - 1;
            int aa = row * this.wIndex + col;
            this.select = aa;
            this.getDetail();
            if (this.select < 0) {
                this.select = 0;
            }

            if (this.select > this.myEquips.size() - 1) {
                this.select = this.myEquips.size() - 1;
            }
        }

        if (CCanvas.keyPressed[2] || CCanvas.keyPressed[4] || CCanvas.keyPressed[6] || CCanvas.keyPressed[8]) {
            if (CCanvas.keyPressed[2]) {
                this.select -= this.wIndex;
            }
            if (CCanvas.keyPressed[8]) {
                this.select += this.wIndex;
            }
            if (CCanvas.keyPressed[4]) {
                this.select--;
            }
            if (CCanvas.keyPressed[6]) {
                this.select++;
            }
            if (select > this.myEquips.size() - 1) {
                select = 0;
            }
            if (select < 0) {
                select = this.myEquips.size() - 1;
            }
            cmtoYI = (select / this.wIndex) * 40 - 20;
            // if (cmtoYI > cm) {
            // cmtoYI = cmyILim;
            // }
            if (cmtoYI < 0) {
                cmtoYI = 0;
            }
            CScreen.clearKey();
        }

    }

    public void reload() {
        this.setCurrEquip();
        this.getBaseAttribute();
    }

}
