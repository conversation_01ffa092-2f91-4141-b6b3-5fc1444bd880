package screen;

import CLib.mGraphics;
import CLib.mImage;
import coreLG.CCanvas;
import effect.Cloud;
import model.Clan;
import model.Font;
import model.IAction;
import model.Language;
import model.TField;
import network.Command;
import network.GameService;

public class TeamCreationScreen extends CScreen {
    private TField tfName;          // Tên
    private TField tfPhone;         // Số điện thoại  
    private TField tfEmail;         // Email
    private TField tfDescription;   // Giới thiệu
    
    private int currentFocus = 0;   // 0=name, 1=phone, 2=email, 3=description
    private CScreen lastScreen;

    // Icon selection - simplified for mobile
    private int selectedIconId = 1;     // Default icon ID
    private mImage selectedIcon = null;
    private boolean isShowingIconSelection = false;
    private int currentIconId = 1;      // Current icon being viewed
    private static final int MIN_ICON_ID = 1;
    private static final int MAX_ICON_ID = 188;
    
    // UI positioning constants
    private static final int FIELD_HEIGHT = 25;
    private static final int FIELD_SPACING = 35;
    private static final int LABEL_OFFSET = 100;
    private static final int START_Y = 120; // Moved down to avoid overlap with icon
    
    public TeamCreationScreen() {
        this.nameCScreen = "TeamCreationScreen";
        initFields();
        initCommands();
    }
    
    private void initFields() {
        int fieldWidth = CCanvas.width - 200; // Narrower to fit in form
        int fieldX = 140; // Offset to account for labels

        // Tên field
        tfName = new TField();
        tfName.x = fieldX;
        tfName.y = START_Y;
        tfName.width = fieldWidth;
        tfName.height = FIELD_HEIGHT;
        tfName.setIputType(TField.INPUT_TYPE_ANY);
        tfName.setisFocus(true);

        // Số điện thoại field
        tfPhone = new TField();
        tfPhone.x = fieldX;
        tfPhone.y = START_Y + FIELD_SPACING;
        tfPhone.width = fieldWidth;
        tfPhone.height = FIELD_HEIGHT;
        tfPhone.setIputType(TField.INPUT_TYPE_NUMERIC);
        tfPhone.setisFocus(false);

        // Email field
        tfEmail = new TField();
        tfEmail.x = fieldX;
        tfEmail.y = START_Y + FIELD_SPACING * 2;
        tfEmail.width = fieldWidth;
        tfEmail.height = FIELD_HEIGHT;
        tfEmail.setIputType(TField.INPUT_TYPE_ANY);
        tfEmail.setisFocus(false);

        // Giới thiệu field (larger)
        tfDescription = new TField();
        tfDescription.x = fieldX;
        tfDescription.y = START_Y + FIELD_SPACING * 3;
        tfDescription.width = fieldWidth;
        tfDescription.height = FIELD_HEIGHT * 2; // Taller for description
        tfDescription.setIputType(TField.INPUT_TYPE_ANY);
        tfDescription.setisFocus(false);
    }
    
    private void initCommands() {
        // Biểu tượng button (left) - để chọn icon
        this.left = new Command("Biểu tượng", new IAction() {
            public void perform() {
                showIconSelection();
            }
        });

        // Tạo đội button (center)
        this.center = new Command("Tạo đội", new IAction() {
            public void perform() {
                doCreateTeam();
            }
        });

        // Thoát button (right)
        this.right = new Command("Thoát", new IAction() {
            public void perform() {
                doClose();
            }
        });
    }
    
    public void show(CScreen lastScreen) {
        this.lastScreen = lastScreen;
        super.show();
    }
    

    
    private void doCreateTeam() {
        String name = tfName.getText().trim();
        String phone = tfPhone.getText().trim();
        String email = tfEmail.getText().trim();
        String description = tfDescription.getText().trim();
        
        // Validate inputs
        if (name.length() < 3) {
            CCanvas.startOKDlg("Tên đội phải có ít nhất 3 ký tự!");
            return;
        }
        
        if (name.length() > 20) {
            CCanvas.startOKDlg("Tên đội không được quá 20 ký tự!");
            return;
        }
        
//        if (phone.length() > 0 && phone.length() < 10) {
//            CCanvas.startOKDlg("Số điện thoại không hợp lệ!");
//            return;
//        }
        
        // TODO: Send team creation request to server
         GameService.gI().createTeam(name, phone, email, description, selectedIconId);
//        CCanvas.startOKDlg("Tạo đội thành công!\nTên đội: " + name + "\nIcon ID: " + selectedIconId);

        // Close screen after successful creation
        doClose();
    }
    
    private void doClose() {
        if (lastScreen != null) {
            lastScreen.show();
        } else {
            CCanvas.menuScr.show();
        }
    }
    
    @Override
    public void paint(mGraphics g) {
        // Paint sky blue background like in the original design
        g.setColor(0x6FAADB); // Sky blue color
        g.fillRect(0, 0, CCanvas.width, CCanvas.hieght, false);

        // Paint clouds
        Cloud.paintCloud(g);

        // Paint title with yellow background like in the design
        g.setColor(0xFFD700); // Gold/yellow color for title background
        g.fillRect(CCanvas.width / 2 - 30, 15, 60, 25, false);
        g.setColor(0x000000); // Black border for title
        g.drawRect(CCanvas.width / 2 - 30, 15, 60, 25, false);
        Font.bigFont.drawString(g, "ĐỘI", CCanvas.width / 2, 17, 3);

        // Paint main form background (light gray area like in design)
        int formX = 50;
        int formY = 60;
        int formW = CCanvas.width - 100;
        int formH = 240; // Increased height to accommodate all fields

        g.setColor(0xC8C8C8); // Light gray background for form
        g.fillRect(formX, formY, formW, formH, false);
        g.setColor(0x000000); // Black border
        g.drawRect(formX, formY, formW, formH, false);

        // Paint icon area with label
        int iconX = formX + 10;
        int iconY = formY + 20;
        int iconSize = 24;

        // Paint "Biểu tượng:" label
        Font.normalFont.drawString(g, "Biểu tượng:", iconX, iconY - 15, 0);

        // Paint clickable icon area
        g.setColor(0xFFFFFF); // White background for icon
        g.fillRect(iconX, iconY, iconSize, iconSize, false);
        g.setColor(0x000000); // Black border
        g.drawRect(iconX, iconY, iconSize, iconSize, false);

        // Paint selected icon
        if (selectedIcon != null) {
            // Scale icon to fit nicely in the box
            g.drawImage(selectedIcon, iconX + 6, iconY + 7, 0, false);
        }

        // Paint field labels with proper positioning
        Font.normalFont.drawString(g, "Tên", formX + 10, tfName.y + 8, 0);
        Font.normalFont.drawString(g, "Số điện thoại", formX + 10, tfPhone.y + 8, 0);
        Font.normalFont.drawString(g, "Email", formX + 10, tfEmail.y + 8, 0);
        Font.normalFont.drawString(g, "Giới thiệu", formX + 10, tfDescription.y + 8, 0);

        // Paint input fields
        tfName.paint(g);
        tfPhone.paint(g);
        tfEmail.paint(g);
        tfDescription.paint(g);

        super.paint(g);

        paintIconSelection(g);
    }

    
    @Override
    public void update() {
        // Update cloud animation
        Cloud.updateCloud();

        // Update text fields
        tfName.update();
        tfPhone.update();
        tfEmail.update();
        tfDescription.update();
    }
    
    @Override
    public void onPointerPressed(int x, int y, int index) {
        super.onPointerPressed(x, y, index);

        // Handle icon selection input first
        if (isShowingIconSelection) {
            handleIconSelectionInput(x, y, index);
            return;
        }

        // Check if user clicked on "Đóng" button in form area
        int buttonY = tfDescription.y + tfDescription.height + 10;
        int buttonWidth = 50;
        int buttonHeight = 20;
        int buttonX = CCanvas.width / 2 - buttonWidth / 2;

        if (CCanvas.isPointer(buttonX, buttonY, buttonWidth, buttonHeight, index)) {
            doClose();
            return;
        }

        // Check if user clicked on icon area to show icon selection
        int formX = 50;
        int formY = 60;
        int iconX = formX + 10;
        int iconY = formY + 20;
        int iconSize = 32;
        if (CCanvas.isPointer(iconX, iconY, iconSize, iconSize, index)) {
            showIconSelection();
            return;
        }

        // Check if user clicked on any text field
        if (CCanvas.isPointer(tfName.x, tfName.y, tfName.width, tfName.height, index)) {
            setFocus(0);
            tfName.doChangeToTextBox();
        } else if (CCanvas.isPointer(tfPhone.x, tfPhone.y, tfPhone.width, tfPhone.height, index)) {
            setFocus(1);
            tfPhone.doChangeToTextBox();
        } else if (CCanvas.isPointer(tfEmail.x, tfEmail.y, tfEmail.width, tfEmail.height, index)) {
            setFocus(2);
            tfEmail.doChangeToTextBox();
        } else if (CCanvas.isPointer(tfDescription.x, tfDescription.y, tfDescription.width, tfDescription.height, index)) {
            setFocus(3);
            tfDescription.doChangeToTextBox();
        }
    }
    
    private void setFocus(int focusIndex) {
        // Clear all focus first
        tfName.setisFocus(false);
        tfPhone.setisFocus(false);
        tfEmail.setisFocus(false);
        tfDescription.setisFocus(false);
        
        // Set focus to selected field
        currentFocus = focusIndex;
        switch (focusIndex) {
            case 0:
                tfName.setisFocus(true);
                break;
            case 1:
                tfPhone.setisFocus(true);
                break;
            case 2:
                tfEmail.setisFocus(true);
                break;
            case 3:
                tfDescription.setisFocus(true);
                break;
        }
    }
    
    @Override
    public void keyPressed(int keyCode) {
        // Handle icon selection navigation
        if (isShowingIconSelection) {
            if (CCanvas.keyPressed[13]) { // Right soft key (Back)
                CCanvas.keyPressed[13] = false;
                hideIconSelection();
                return;
            }
            if (CCanvas.keyPressed[4]) { // Left key - previous icon
                CCanvas.keyPressed[4] = false;
                prevIcon();
                return;
            }
            if (CCanvas.keyPressed[6]) { // Right key - next icon
                CCanvas.keyPressed[6] = false;
                nextIcon();
                return;
            }
            if (CCanvas.keyPressed[12]) { // Left soft key (Select)
                CCanvas.keyPressed[12] = false;
                selectCurrentIcon();
                return;
            }
            return;
        }

        // Handle navigation between fields using CCanvas key constants
        if (CCanvas.keyPressed[2]) { // UP key
            CCanvas.keyPressed[2] = false;
            if (currentFocus > 0) {
                setFocus(currentFocus - 1);
            }
        } else if (CCanvas.keyPressed[8]) { // DOWN key
            CCanvas.keyPressed[8] = false;
            if (currentFocus < 3) {
                setFocus(currentFocus + 1);
            }
        } else {
            // Pass key events to focused field
            switch (currentFocus) {
                case 0:
                    tfName.keyPressed(keyCode);
                    break;
                case 1:
                    tfPhone.keyPressed(keyCode);
                    break;
                case 2:
                    tfEmail.keyPressed(keyCode);
                    break;
                case 3:
                    tfDescription.keyPressed(keyCode);
                    break;
            }
        }

        super.keyPressed(keyCode);
    }

    private void loadDefaultIcon() {
        // Load default icon (ID = 1)
        GameService.gI().getClanIcon((short) selectedIconId);
    }

    private void showIconSelection() {
        isShowingIconSelection = true;
        currentIconId = selectedIconId; // Start with currently selected icon

        // Load current icon if not exists
        if (!CCanvas.iconMn.isExist(currentIconId)) {
            GameService.gI().getClanIcon((short) currentIconId);
        }
    }

    private void loadCurrentIcon() {
        if (!CCanvas.iconMn.isExist(currentIconId)) {
            GameService.gI().getClanIcon((short) currentIconId);
        }
    }

    private void hideIconSelection() {
        isShowingIconSelection = false;
    }

    private void selectCurrentIcon() {
        selectedIconId = currentIconId;
        if (CCanvas.iconMn.isExist(currentIconId)) {
            selectedIcon = new mImage(CCanvas.iconMn.getImage(currentIconId));
        }
        hideIconSelection();
    }

    private void nextIcon() {
        if (currentIconId < MAX_ICON_ID) {
            currentIconId++;
            loadCurrentIcon();
        }
    }

    private void prevIcon() {
        if (currentIconId > MIN_ICON_ID) {
            currentIconId--;
            loadCurrentIcon();
        }
    }

    private void paintIconSelection(mGraphics g) {
        if (!isShowingIconSelection) return;

//        // Paint semi-transparent overlay
//        g.setColor(0x000000);
//        g.fillRect(0, 0, CCanvas.width, CCanvas.hieght, true);

        // Paint icon selection dialog - mobile friendly
        int dialogW = Math.min(280, CCanvas.width - 40);
        int dialogH = 180;
        int dialogX = (CCanvas.width - dialogW) / 2;
        int dialogY = (CCanvas.hieght - dialogH) / 2;

        // Dialog background
        g.setColor(0xFFFFFF);
        g.fillRect(dialogX, dialogY, dialogW, dialogH, false);
        g.setColor(0x000000);
        g.drawRect(dialogX, dialogY, dialogW, dialogH, false);

        // Title
        Font.bigFont.drawString(g, "BIỂU TƯỢNG", dialogX + dialogW/2, dialogY + 15, 3);

        // Current icon display area
        int iconDisplaySize = 24;
        int iconDisplayX = dialogX + (dialogW - iconDisplaySize) / 2;
        int iconDisplayY = dialogY + 40;

        // Paint icon background
        g.setColor(0xF0F0F0);
        g.fillRect(iconDisplayX, iconDisplayY, iconDisplaySize, iconDisplaySize, false);
        g.setColor(0x000000);
        g.drawRect(iconDisplayX, iconDisplayY, iconDisplaySize, iconDisplaySize, false);

        // Paint current icon
        if (CCanvas.iconMn.isExist(currentIconId)) {
            mImage icon = new mImage(CCanvas.iconMn.getImage(currentIconId));
            g.drawImage(icon, iconDisplayX+6 , iconDisplayY+6, 0, false);
        } else {
            g.setColor(0x808080);
            Font.normalFont.drawString(g, "Loading...", iconDisplayX + iconDisplaySize/2, iconDisplayY + iconDisplaySize/2, 3);
        }

        // Icon ID display
        Font.normalFont.drawString(g, "ID: " + currentIconId + " / " + MAX_ICON_ID,
                                  dialogX + dialogW/2, iconDisplayY + iconDisplaySize + 15, 3);


        // Navigation buttons
        int buttonW = 60;
        int buttonH = 30;
        int buttonY = dialogY + dialogH - buttonH - 15;

        // Previous button
        int prevButtonX = dialogX + 20;
        g.setColor(12965614);
        g.fillRect(dialogX, buttonY, dialogW, 30, false);
        g.setColor(12965614);
        g.drawRect(dialogX, buttonY, dialogW, 30, false);
//        g.setColor(currentIconId > MIN_ICON_ID ? 0x4CAF50 : 0xCCCCCC);
//        g.fillRect(prevButtonX, buttonY, buttonW, buttonH, false);
//        g.setColor(0x000000);
//        g.drawRect(prevButtonX, buttonY, buttonW, buttonH, false);
        Font.normalFont.drawString(g, "◀ Trước", prevButtonX + buttonW/2, buttonY + buttonH/2 - 5, 3);

        // Next button
        int nextButtonX = dialogX + dialogW - buttonW - 20;
//        g.setColor(currentIconId < MAX_ICON_ID ? 0x4CAF50 : 0xCCCCCC);
//        g.fillRect(nextButtonX, buttonY, buttonW, buttonH, false);
//        g.setColor(0x000000);
//        g.drawRect(nextButtonX, buttonY, buttonW, buttonH, false);
        Font.normalFont.drawString(g, "Sau ▶", nextButtonX + buttonW/2, buttonY + buttonH/2 - 5, 3);

        // Select and Close buttons
        int selectButtonW = 50;
        int selectButtonX = dialogX + (dialogW - selectButtonW) / 2 - 30;
        int closeButtonX = dialogX + (dialogW - selectButtonW) / 2 + 30;

        // Select button
//        g.setColor(0xFFD700);
//        g.fillRect(selectButtonX, buttonY, selectButtonW, buttonH, false);
//        g.setColor(0x000000);
//        g.drawRect(selectButtonX, buttonY, selectButtonW, buttonH, false);
        Font.normalFont.drawString(g, "Chọn", selectButtonX + selectButtonW/2, buttonY + buttonH/2 - 5, 3);

        // Close button
        // Paint button bar background
        Font.normalFont.drawString(g, "Đóng", closeButtonX + selectButtonW/2, buttonY + buttonH/2 - 5, 3);
    }

    private void handleIconSelectionInput(int x, int y, int index) {
        if (!isShowingIconSelection) return;

        int dialogW = Math.min(280, CCanvas.width - 40);
        int dialogH = 180;
        int dialogX = (CCanvas.width - dialogW) / 2;
        int dialogY = (CCanvas.hieght - dialogH) / 2;

        // Check if clicked outside dialog to close
        if (!CCanvas.isPointer(dialogX, dialogY, dialogW, dialogH, index)) {
            hideIconSelection();
            return;
        }

        // Button dimensions
        int buttonW = 60;
        int buttonH = 30;
        int buttonY = dialogY + dialogH - buttonH - 15;

        // Check Previous button
        int prevButtonX = dialogX + 20;
        if (CCanvas.isPointer(prevButtonX, buttonY, buttonW, buttonH, index)) {
            prevIcon();
            return;
        }

        // Check Next button
        int nextButtonX = dialogX + dialogW - buttonW - 20;
        if (CCanvas.isPointer(nextButtonX, buttonY, buttonW, buttonH, index)) {
            nextIcon();
            return;
        }

        // Check Select button
        int selectButtonW = 50;
        int selectButtonX = dialogX + (dialogW - selectButtonW) / 2 - 30;
        if (CCanvas.isPointer(selectButtonX, buttonY, selectButtonW, buttonH, index)) {
            selectCurrentIcon();
            return;
        }

        // Check Close button
        int closeButtonX = dialogX + (dialogW - selectButtonW) / 2 + 30;
        if (CCanvas.isPointer(closeButtonX, buttonY, selectButtonW, buttonH, index)) {
            hideIconSelection();
            return;
        }
    }

    // Method to be called when icon is loaded from server
    public void onIconLoaded(int iconId) {
        // Update selected icon if it matches
        if (iconId == selectedIconId && selectedIcon == null) {
            if (CCanvas.iconMn.isExist(iconId)) {
                selectedIcon = new mImage(CCanvas.iconMn.getImage(iconId));
            }
        }
        // No need to do anything for currentIconId as it will be handled in paint method
    }

    // Getter for selected icon ID (for external use)
    public int getSelectedIconId() {
        return selectedIconId;
    }


}
