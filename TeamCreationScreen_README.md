# TeamCreationScreen - <PERSON><PERSON><PERSON> hình tạo đội

## <PERSON><PERSON> tả
TeamCreationScreen là một màn hình tạo đội mới trong game MobiArmy2, đ<PERSON><PERSON><PERSON> thiết kế để giống với giao diện gốc trong hình ảnh được cung cấp.

## T<PERSON>h năng đã implement

### Gia<PERSON> diện
- **Background**: <PERSON><PERSON><PERSON> xanh da trời với hiệu ứng mây di chuyển
- **Title**: Tiêu đề "ĐỘI" với nền vàng
- **Form**: Khung form màu xám nhạt chứa các trường nhập liệu
- **Styling**: <PERSON><PERSON> hợp với thiết kế gốc của game

### Các trường nhập liệu
1. **Icon**: Chọn icon cho đội (ID từ 1-188)
2. **Tên**: <PERSON><PERSON><PERSON> (b<PERSON><PERSON> bu<PERSON>, 3-20 ký tự)
3. **<PERSON><PERSON> điện thoại**: <PERSON><PERSON> điện thoại (t<PERSON><PERSON> chọ<PERSON>, chỉ số)
4. **Email**: Địa chỉ email (tùy chọn)
5. **Giới thiệu**: Mô tả về đội (tùy chọn, trường lớn hơn)

### Điều khiển
- **Keyboard**: Sử dụng phím UP/DOWN để di chuyển giữa các trường
- **Touch**: Chạm vào trường để chọn và hiện input dialog
- **Icon Selection**: Chạm vào icon để mở dialog chọn icon
- **Validation**: Kiểm tra dữ liệu đầu vào

### Các nút chức năng
- **Biểu tượng** (trái): Mở dialog chọn icon cho đội
- **Tạo đội** (giữa): Tạo đội mới với validation
- **Thoát** (phải): Đóng màn hình
- **Đóng** (trong form): Nút đóng bổ sung trong form
- **Đóng** (trong icon dialog): Đóng dialog chọn icon

## Cách sử dụng

### 1. Từ code
```java
// Hiển thị màn hình tạo đội
CCanvas.teamCreationScreen.show(currentScreen);

// Hoặc sử dụng method helper
CCanvas.showTeamCreationScreen();
```

### 2. Tích hợp vào menu
Thêm vào menu của game:
```java
Command createTeamCmd = new Command("Tạo đội", new IAction() {
    public void perform() {
        CCanvas.teamCreationScreen.show(MenuScr.this);
    }
});
```

### 3. Testing
Chạy file test:
```java
TeamCreationScreenTest.showTeamCreationScreen();
```

## Cấu trúc file

### Core files
- `core/src/screen/TeamCreationScreen.java` - Main screen class
- `core/src/coreLG/CCanvas.java` - Updated to include TeamCreationScreen
- `core/src/test/TeamCreationScreenTest.java` - Test utilities

### Dependencies
- `CScreen` - Base screen class
- `TField` - Text input fields
- `Cloud` - Cloud animation effects
- `Font` - Text rendering
- `Command` - Button handling

## Validation Rules

### Tên đội
- Tối thiểu 3 ký tự
- Tối đa 20 ký tự
- Không được để trống

### Số điện thoại
- Chỉ chấp nhận số
- Nếu nhập thì phải ít nhất 10 số

### Email & Giới thiệu
- Tùy chọn, không có validation đặc biệt

## Customization

### Màu sắc
```java
// Sky blue background
g.setColor(0x6FAADB);

// Form background
g.setColor(0xC8C8C8);

// Button background
g.setColor(0xFFD700);
```

### Positioning (Updated)
```java
// Form dimensions
int formX = 50;
int formY = 60;
int formW = CCanvas.width - 100;
int formH = 240; // Increased height

// Icon positioning
int iconX = formX + 10;
int iconY = formY + 20;
int iconSize = 32;

// Text fields positioning
int fieldX = 140; // Offset for labels
int startY = 120; // Moved down to avoid overlap
```

### Debug Tools
```java
// Check layout positioning
TeamCreationScreenTest.debugLayout();

// Show detailed position info
CCanvas.teamCreationScreen.debugLayout();
```

## New Features Added

### Icon Selection System (Mobile-Friendly)
- **Single Icon Display**: Hiển thị 1 icon lớn tại một thời điểm (64x64px)
- **Navigation Buttons**: Nút "◀ Trước" và "Sau ▶" để browse icons
- **Auto Loading**: Tự động request icons từ server khi cần
- **Icon Cache**: Sử dụng IconManager để cache icons
- **Touch & Keyboard**: Hỗ trợ cả touch và keyboard navigation
- **Mobile Optimized**: Thiết kế phù hợp cho Android/iOS

### Fixed Issues
- **Input Dialog**: Sửa lỗi TField không hiện input dialog khi chạm
- **Icon Integration**: Tích hợp với GameService.getClanIcon()
- **Icon Display**: Hiển thị icon đã chọn trong form
- **Layout Positioning**: Sửa lỗi icon đè lên text fields
- **Icon Selection UI**: Thiết kế lại hoàn toàn cho mobile với single icon display
- **Form Layout**: Điều chỉnh kích thước và vị trí các elements để không overlap

## Mobile-Friendly Icon Selection

### New Design Features
- **Compact Dialog**: 280px width, 180px height - phù hợp mọi screen size
- **Large Icon Display**: 64x64px icon display area với loading indicator
- **Clear Navigation**: Nút "◀ Trước" và "Sau ▶" với visual feedback
- **Action Buttons**: "Chọn" (vàng) và "Đóng" (đỏ) buttons rõ ràng
- **ID Counter**: Hiển thị "ID: X / 188" để user biết vị trí hiện tại
- **Keyboard Support**: LEFT/RIGHT arrows, soft keys

### Cross-Platform Benefits
- **Touch Friendly**: Buttons đủ lớn cho finger touch
- **Performance**: Chỉ load 1 icon tại một thời điểm thay vì 32 icons
- **Memory Efficient**: Giảm memory usage đáng kể
- **Network Optimized**: Chỉ request icon khi cần thiết
- **Responsive**: Tự động scale theo screen size

## Future Enhancements
- Kết nối với server để tạo đội thực sự
- Thêm jump-to-ID functionality
- Thêm favorite icons system
- Thêm settings cho đội (public/private, etc.)
- Thêm preview cho đội đã tạo
- Thêm sound effects

## Notes
- Screen đã được tích hợp vào CCanvas.loadScreen()
- Sử dụng animation cloud từ game gốc
- Tương thích với cả keyboard và touch input
- Responsive design phù hợp với các kích thước màn hình khác nhau
